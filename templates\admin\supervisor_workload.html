{% extends "base.html" %}

{% block title %}Supervisor Workload - Admin Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users me-2"></i>Manage Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('assign_supervisor') }}">
                    <i class="fas fa-user-tie me-2"></i>Assign Supervisors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_workload') }}">
                    <i class="fas fa-chart-bar me-2"></i>Supervisor Workload
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_system') }}">
                    <i class="fas fa-cog me-2"></i>System Settings
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar me-2"></i>Supervisor Workload Analysis
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group">
            <a href="{{ url_for('assign_supervisor') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Assignment
            </a>
        </div>
    </div>
</div>

<!-- Workload Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">School Supervisors</h5>
                        <h2 class="mb-0">{{ school_workload|length }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-university fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Industry Supervisors</h5>
                        <h2 class="mb-0">{{ industry_workload|length }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Avg School Load</h5>
                        <h2 class="mb-0">
                            {% if school_workload %}
                                {{ (school_workload|sum(attribute='workload') / school_workload|length)|round(1) }}
                            {% else %}
                                0
                            {% endif %}
                        </h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Avg Industry Load</h5>
                        <h2 class="mb-0">
                            {% if industry_workload %}
                                {{ (industry_workload|sum(attribute='workload') / industry_workload|length)|round(1) }}
                            {% else %}
                                0
                            {% endif %}
                        </h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-university me-2"></i>School Supervisor Workload
                </h5>
            </div>
            <div class="card-body">
                {% if school_workload %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Supervisor</th>
                                <th>Institution</th>
                                <th>Department</th>
                                <th>Students</th>
                                <th>Load Level</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in school_workload %}
                            <tr>
                                <td>
                                    <strong>{{ item.supervisor.name }}</strong><br>
                                    <small class="text-muted">{{ item.supervisor.email }}</small>
                                </td>
                                <td>{{ item.profile.institution }}</td>
                                <td>{{ item.profile.department }}</td>
                                <td class="text-center">
                                    <span class="badge bg-primary">{{ item.workload }}</span>
                                </td>
                                <td>
                                    {% if item.workload == 0 %}
                                        <span class="badge bg-secondary">No Load</span>
                                    {% elif item.workload <= 3 %}
                                        <span class="badge bg-success">Light</span>
                                    {% elif item.workload <= 6 %}
                                        <span class="badge bg-warning">Moderate</span>
                                    {% else %}
                                        <span class="badge bg-danger">Heavy</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-university fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No School Supervisors</h5>
                    <p class="text-muted">No school supervisors with profiles found.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>Industry Supervisor Workload
                </h5>
            </div>
            <div class="card-body">
                {% if industry_workload %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Supervisor</th>
                                <th>Company</th>
                                <th>Industry</th>
                                <th>Students</th>
                                <th>Load Level</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in industry_workload %}
                            <tr>
                                <td>
                                    <strong>{{ item.supervisor.name }}</strong><br>
                                    <small class="text-muted">{{ item.supervisor.email }}</small>
                                </td>
                                <td>{{ item.profile.company_name }}</td>
                                <td>{{ item.profile.industry_type }}</td>
                                <td class="text-center">
                                    <span class="badge bg-primary">{{ item.workload }}</span>
                                </td>
                                <td>
                                    {% if item.workload == 0 %}
                                        <span class="badge bg-secondary">No Load</span>
                                    {% elif item.workload <= 3 %}
                                        <span class="badge bg-success">Light</span>
                                    {% elif item.workload <= 6 %}
                                        <span class="badge bg-warning">Moderate</span>
                                    {% else %}
                                        <span class="badge bg-danger">Heavy</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Industry Supervisors</h5>
                    <p class="text-muted">No industry supervisors with profiles found.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Least Loaded Assignment Algorithm
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>How It Works</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Finds supervisors with matching criteria</li>
                            <li><i class="fas fa-check text-success me-2"></i>Calculates current student load for each</li>
                            <li><i class="fas fa-check text-success me-2"></i>Assigns to supervisor with lowest load</li>
                            <li><i class="fas fa-check text-success me-2"></i>Ensures balanced workload distribution</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Matching Criteria</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-university text-primary me-2"></i><strong>School:</strong> Same institution & department</li>
                            <li><i class="fas fa-building text-success me-2"></i><strong>Industry:</strong> Available supervisors</li>
                            <li><i class="fas fa-user-check text-info me-2"></i><strong>Status:</strong> Active supervisors only</li>
                            <li><i class="fas fa-id-card text-warning me-2"></i><strong>Profile:</strong> Complete profiles required</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <strong>Load Level Guidelines:</strong>
                    <span class="badge bg-success ms-2">Light (1-3 students)</span>
                    <span class="badge bg-warning ms-2">Moderate (4-6 students)</span>
                    <span class="badge bg-danger ms-2">Heavy (7+ students)</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
