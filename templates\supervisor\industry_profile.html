{% extends "base.html" %}

{% block title %}Industry Supervisor Profile - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>Industry Supervisor Profile
    </h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>Company Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_name" class="form-label">Company Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="company_name" name="company_name" 
                                   value="{{ profile.company_name if profile else '' }}" required
                                   placeholder="e.g., Microsoft Nigeria">
                            <div class="form-text">The company where you work</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="industry_type" class="form-label">Industry Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="industry_type" name="industry_type" required>
                                <option value="">Select Industry</option>
                                <option value="Information Technology" {{ 'selected' if profile and profile.industry_type == 'Information Technology' else '' }}>Information Technology</option>
                                <option value="Engineering" {{ 'selected' if profile and profile.industry_type == 'Engineering' else '' }}>Engineering</option>
                                <option value="Finance & Banking" {{ 'selected' if profile and profile.industry_type == 'Finance & Banking' else '' }}>Finance & Banking</option>
                                <option value="Healthcare" {{ 'selected' if profile and profile.industry_type == 'Healthcare' else '' }}>Healthcare</option>
                                <option value="Manufacturing" {{ 'selected' if profile and profile.industry_type == 'Manufacturing' else '' }}>Manufacturing</option>
                                <option value="Oil & Gas" {{ 'selected' if profile and profile.industry_type == 'Oil & Gas' else '' }}>Oil & Gas</option>
                                <option value="Telecommunications" {{ 'selected' if profile and profile.industry_type == 'Telecommunications' else '' }}>Telecommunications</option>
                                <option value="Construction" {{ 'selected' if profile and profile.industry_type == 'Construction' else '' }}>Construction</option>
                                <option value="Education" {{ 'selected' if profile and profile.industry_type == 'Education' else '' }}>Education</option>
                                <option value="Media & Entertainment" {{ 'selected' if profile and profile.industry_type == 'Media & Entertainment' else '' }}>Media & Entertainment</option>
                                <option value="Agriculture" {{ 'selected' if profile and profile.industry_type == 'Agriculture' else '' }}>Agriculture</option>
                                <option value="Retail & Commerce" {{ 'selected' if profile and profile.industry_type == 'Retail & Commerce' else '' }}>Retail & Commerce</option>
                                <option value="Transportation" {{ 'selected' if profile and profile.industry_type == 'Transportation' else '' }}>Transportation</option>
                                <option value="Other" {{ 'selected' if profile and profile.industry_type == 'Other' else '' }}>Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">Your Position <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="position" name="position" 
                                   value="{{ profile.position if profile else '' }}" required
                                   placeholder="e.g., Software Engineer, Project Manager">
                            <div class="form-text">Your job title in the company</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">Department</label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   value="{{ profile.department if profile else '' }}"
                                   placeholder="e.g., IT Department, Engineering">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="company_address" class="form-label">Company Address</label>
                        <textarea class="form-control" id="company_address" name="company_address" rows="3"
                                  placeholder="Full company address">{{ profile.company_address if profile else '' }}</textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="company_size" class="form-label">Company Size</label>
                            <select class="form-select" id="company_size" name="company_size">
                                <option value="">Select Size</option>
                                <option value="Small (1-50 employees)" {{ 'selected' if profile and profile.company_size == 'Small (1-50 employees)' else '' }}>Small (1-50 employees)</option>
                                <option value="Medium (51-250 employees)" {{ 'selected' if profile and profile.company_size == 'Medium (51-250 employees)' else '' }}>Medium (51-250 employees)</option>
                                <option value="Large (251-1000 employees)" {{ 'selected' if profile and profile.company_size == 'Large (251-1000 employees)' else '' }}>Large (251-1000 employees)</option>
                                <option value="Enterprise (1000+ employees)" {{ 'selected' if profile and profile.company_size == 'Enterprise (1000+ employees)' else '' }}>Enterprise (1000+ employees)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="years_of_experience" class="form-label">Years of Experience</label>
                            <input type="number" class="form-control" id="years_of_experience" name="years_of_experience" 
                                   value="{{ profile.years_of_experience if profile else '' }}" min="0" max="50">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="specialization" class="form-label">Area of Specialization</label>
                        <input type="text" class="form-control" id="specialization" name="specialization" 
                               value="{{ profile.specialization if profile else '' }}"
                               placeholder="e.g., Web Development, Network Security, Project Management">
                    </div>

                    <!-- Digital Signature Upload -->
                    <div class="mb-3">
                        <label for="signature_file" class="form-label">Digital Signature</label>
                        <input type="file" class="form-control" id="signature_file" name="signature_file"
                               accept=".png,.jpg,.jpeg,.gif,.bmp,.webp">
                        <div class="form-text">Upload your digital signature (PNG, JPG, JPEG, GIF, BMP, WEBP only)</div>
                        {% if profile and profile.signature_filename %}
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>Current signature: {{ profile.signature_filename }}
                            </small>
                            <div class="mt-2">
                                <img src="{{ url_for('uploaded_signature', filename=profile.signature_filename) }}"
                                     alt="Current Signature" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('supervisor_dashboard') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Account Information
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ current_user.name }}</p>
                <p><strong>Email:</strong> {{ current_user.email }}</p>
                <p><strong>Phone:</strong> {{ current_user.phone or 'Not provided' }}</p>
                <p><strong>Role:</strong> {{ current_user.role.name }}</p>
                <p><strong>Member Since:</strong> {{ current_user.created_at.strftime('%B %d, %Y') }}</p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Profile Guidelines
                </h5>
            </div>
            <div class="card-body">
                <h6>Required Information</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Company name</li>
                    <li><i class="fas fa-check text-success me-2"></i>Industry type</li>
                    <li><i class="fas fa-check text-success me-2"></i>Your position</li>
                </ul>

                <h6 class="mt-3">Supervision Benefits</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-users text-primary me-2"></i>Mentor students in your field</li>
                    <li><i class="fas fa-users text-primary me-2"></i>Share industry experience</li>
                    <li><i class="fas fa-users text-primary me-2"></i>Build future talent pipeline</li>
                </ul>

                <div class="alert alert-info mt-3">
                    <small>
                        <strong>Note:</strong> Students will be assigned based on their SIWES organization and your industry expertise.
                    </small>
                </div>
            </div>
        </div>

        {% if profile %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Assignment Eligibility
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h6 class="text-success">Profile Complete</h6>
                    <p class="text-muted">You can now be assigned to students in:</p>
                    <div class="alert alert-success">
                        <strong>{{ profile.industry_type }}</strong><br>
                        <small>{{ profile.position }} at {{ profile.company_name }}</small>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Profile Status
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h6 class="text-warning">Profile Incomplete</h6>
                    <p class="text-muted">Complete your profile to be eligible for student assignments.</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
