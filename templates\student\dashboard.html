{% extends "base.html" %}

{% block title %}Student Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('student_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_profile') }}">
                    <i class="fas fa-user me-2"></i>Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('new_log_entry') }}">
                    <i class="fas fa-plus me-2"></i>New Log Entry
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('view_school_supervisor') }}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>School Supervisor
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_logs') }}">
                    <i class="fas fa-list me-2"></i>View Logs
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt me-2"></i>Student Dashboard
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('new_log_entry') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>New Log Entry
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-primary stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Log Entries</h5>
                        <h2 class="mb-0">{{ total_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-white bg-success stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Approved Entries</h5>
                        <h2 class="mb-0">{{ approved_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-white bg-warning stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Pending Review</h5>
                        <h2 class="mb-0">{{ pending_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card text-white bg-info stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Progress</h5>
                        <h2 class="mb-0">{{ ((approved_logs / total_logs * 100) | round(1)) if total_logs > 0 else 0 }}%</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Recent Log Entries
                </h5>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Activities</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>{{ log.log_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ log.activities_performed[:50] }}{% if log.activities_performed|length > 50 %}...{% endif %}</td>
                                    <td>
                                        {% if log.status == 'Pending' %}
                                            <span class="badge bg-warning">{{ log.status }}</span>
                                        {% elif log.status == 'Approved' %}
                                            <span class="badge bg-success">{{ log.status }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ log.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-book fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No log entries yet</h5>
                        <p class="text-muted">Start by creating your first log entry!</p>
                        <a href="{{ url_for('new_log_entry') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Log Entry
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Profile Information
                </h5>
            </div>
            <div class="card-body">
                <!-- Profile Picture Section -->
                <div class="text-center mb-3">
                    {% if current_user.profile_picture %}
                        <img src="{{ url_for('uploaded_profile', filename=current_user.profile_picture) }}"
                             alt="Profile Picture"
                             class="dashboard-profile-picture mb-2">
                    {% else %}
                        <div class="dashboard-avatar-circle mb-2">
                            {{ current_user.name[0].upper() }}
                        </div>
                    {% endif %}
                    <div>
                        <a href="{{ url_for('profile_picture') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-camera me-1"></i>
                            {% if current_user.profile_picture %}Update{% else %}Add{% endif %} Photo
                        </a>
                    </div>
                </div>

                {% if student %}
                    <p><strong>Matric Number:</strong> {{ student.matric_number }}</p>
                    <p><strong>Department:</strong> {{ student.department or 'Not set' }}</p>
                    <p><strong>Institution:</strong> {{ student.institution or 'Not set' }}</p>
                    <p><strong>Organization:</strong> {{ student.siwes_organization_name or 'Not set' }}</p>
                    {% if student.siwes_start_date %}
                        <p><strong>SIWES Period:</strong><br>
                        {{ student.siwes_start_date.strftime('%Y-%m-%d') }} to
                        {{ student.siwes_end_date.strftime('%Y-%m-%d') if student.siwes_end_date else 'Not set' }}</p>
                    {% endif %}
                    <a href="{{ url_for('student_profile') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-edit me-1"></i>Edit Profile
                    </a>
                {% else %}
                    <div class="text-center">
                        <i class="fas fa-user-plus fa-2x text-muted mb-3"></i>
                        <p class="text-muted">Complete your profile to get started</p>
                        <a href="{{ url_for('student_profile') }}" class="btn btn-primary">
                            <i class="fas fa-user-edit me-2"></i>Complete Profile
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Supervisors
                </h5>
            </div>
            <div class="card-body">
                {% if student and (student.industry_supervisor or student.school_supervisor) %}
                    {% if student.industry_supervisor %}
                        <div class="mb-3">
                            <p><strong>Industry Supervisor:</strong><br>
                            {{ student.industry_supervisor.name }}</p>
                            <small class="text-muted">{{ student.industry_supervisor.email }}</small>
                        </div>
                    {% endif %}
                    {% if student.school_supervisor %}
                        <div class="mb-3">
                            <p><strong>School Supervisor:</strong><br>
                            {{ student.school_supervisor.name }}</p>
                            <small class="text-muted">{{ student.school_supervisor.email }}</small>
                        </div>
                        <div class="d-grid">
                            <a href="{{ url_for('view_school_supervisor') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a>
                        </div>
                    {% endif %}
                {% else %}
                    <p class="text-muted">No supervisors assigned yet</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.dashboard-profile-picture {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dashboard-avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2rem;
    margin: 0 auto;
    border: 3px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
