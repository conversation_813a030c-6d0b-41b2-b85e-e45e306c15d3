#!/usr/bin/env python3
"""
Test Data Verification Script for SIWES System
Verifies that all test data was created successfully
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Role, Student, SchoolSupervisor, IndustrySupervisor, ITFSupervisor

def verify_test_data():
    """Verify all test data was created successfully"""
    print("🔍 Verifying test data...")
    print("=" * 50)
    
    with app.app_context():
        # Check roles
        roles = Role.query.all()
        print(f"📋 Roles: {len(roles)} found")
        for role in roles:
            user_count = User.query.filter_by(role_id=role.id).count()
            print(f"   • {role.name}: {user_count} users")
        
        print()
        
        # Check students
        students = Student.query.all()
        print(f"🎓 Students: {len(students)} profiles created")
        
        states = {}
        institutions = {}
        departments = {}
        
        for student in students:
            # Count by state
            state = student.siwes_state or 'Unknown'
            states[state] = states.get(state, 0) + 1
            
            # Count by institution
            institution = student.institution or 'Unknown'
            institutions[institution] = institutions.get(institution, 0) + 1
            
            # Count by department
            department = student.department or 'Unknown'
            departments[department] = departments.get(department, 0) + 1
        
        print("   States distribution:")
        for state, count in states.items():
            print(f"     • {state}: {count} students")
        
        print("   Institutions distribution:")
        for institution, count in institutions.items():
            print(f"     • {institution}: {count} students")
        
        print("   Departments distribution:")
        for department, count in departments.items():
            print(f"     • {department}: {count} students")
        
        print()
        
        # Check school supervisors
        school_supervisors = SchoolSupervisor.query.all()
        print(f"🏫 School Supervisors: {len(school_supervisors)} profiles created")
        
        school_institutions = {}
        school_departments = {}
        
        for supervisor in school_supervisors:
            institution = supervisor.institution or 'Unknown'
            school_institutions[institution] = school_institutions.get(institution, 0) + 1
            
            department = supervisor.department or 'Unknown'
            school_departments[department] = school_departments.get(department, 0) + 1
        
        print("   Institutions:")
        for institution, count in school_institutions.items():
            print(f"     • {institution}: {count} supervisors")
        
        print("   Departments:")
        for department, count in school_departments.items():
            print(f"     • {department}: {count} supervisors")
        
        print()
        
        # Check industry supervisors
        industry_supervisors = IndustrySupervisor.query.all()
        print(f"🏢 Industry Supervisors: {len(industry_supervisors)} profiles created")
        
        industries = {}
        companies = {}
        
        for supervisor in industry_supervisors:
            industry = supervisor.industry_type or 'Unknown'
            industries[industry] = industries.get(industry, 0) + 1
            
            company = supervisor.company_name or 'Unknown'
            companies[company] = companies.get(company, 0) + 1
        
        print("   Industries:")
        for industry, count in industries.items():
            print(f"     • {industry}: {count} supervisors")
        
        print("   Companies:")
        for company, count in companies.items():
            print(f"     • {company}: {count} supervisors")
        
        print()
        
        # Check ITF supervisors
        itf_supervisors = ITFSupervisor.query.all()
        print(f"👨‍💼 ITF Supervisors: {len(itf_supervisors)} profiles created")
        
        itf_states = {}
        
        for supervisor in itf_supervisors:
            state = supervisor.assigned_state or 'Unknown'
            itf_states[state] = itf_states.get(state, 0) + 1
        
        print("   Assigned States:")
        for state, count in itf_states.items():
            print(f"     • {state}: {count} supervisors")
        
        print()
        print("=" * 50)
        print("✅ Test data verification completed!")
        
        # Check for potential automatic assignments
        unassigned_students = Student.query.filter(
            (Student.school_supervisor_id.is_(None)) & 
            (Student.industry_supervisor_id.is_(None))
        ).count()
        
        print(f"📊 Assignment Status:")
        print(f"   • Total students: {len(students)}")
        print(f"   • Unassigned students: {unassigned_students}")
        print(f"   • Ready for auto-assignment: {unassigned_students > 0}")

if __name__ == '__main__':
    verify_test_data()
