{% extends "base.html" %}

{% block title %}Profile Picture - SIWES Reporting System{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-camera me-2"></i>Profile Picture
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Current Profile Picture -->
                    <div class="text-center mb-4">
                        {% if current_user.profile_picture %}
                            <img src="{{ url_for('uploaded_profile', filename=current_user.profile_picture) }}" 
                                 alt="Profile Picture" 
                                 class="profile-picture-large mb-3">
                            <p class="text-muted">Current profile picture</p>
                        {% else %}
                            <div class="profile-picture-placeholder mb-3">
                                <i class="fas fa-user fa-4x text-muted"></i>
                            </div>
                            <p class="text-muted">No profile picture uploaded</p>
                        {% endif %}
                    </div>

                    <!-- Upload Form -->
                    <form method="POST" enctype="multipart/form-data" id="profilePictureForm">
                        <div class="mb-3">
                            <label for="profile_picture" class="form-label">
                                <i class="fas fa-upload me-2"></i>Choose New Profile Picture
                            </label>
                            <input type="file" 
                                   class="form-control" 
                                   id="profile_picture" 
                                   name="profile_picture" 
                                   accept="image/*"
                                   required>
                            <div class="form-text">
                                Supported formats: PNG, JPG, JPEG, GIF, BMP, WEBP. Maximum size: 50MB.
                            </div>
                        </div>

                        <!-- Image Preview -->
                        <div class="mb-3" id="imagePreview" style="display: none;">
                            <label class="form-label">Preview:</label>
                            <div class="text-center">
                                <img id="previewImage" src="" alt="Preview" class="profile-picture-preview">
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Upload Picture
                            </button>
                            
                            {% if current_user.profile_picture %}
                                <form method="POST" action="{{ url_for('delete_profile_picture_route') }}" style="display: inline;">
                                    <button type="submit" 
                                            class="btn btn-outline-danger"
                                            onclick="return confirm('Are you sure you want to delete your profile picture?')">
                                        <i class="fas fa-trash me-2"></i>Delete Picture
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </form>

                    <!-- Guidelines -->
                    <div class="mt-4">
                        <h6>
                            <i class="fas fa-info-circle me-2"></i>Guidelines
                        </h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Use a clear, professional photo</li>
                            <li><i class="fas fa-check text-success me-2"></i>Face should be clearly visible</li>
                            <li><i class="fas fa-check text-success me-2"></i>Square images work best</li>
                            <li><i class="fas fa-check text-success me-2"></i>Avoid group photos or inappropriate content</li>
                            <li><i class="fas fa-check text-success me-2"></i>Image will be automatically resized if needed</li>
                        </ul>
                    </div>

                    <!-- Navigation -->
                    <div class="mt-4 text-center">
                        {% if current_user.role.name == 'Student' %}
                            <a href="{{ url_for('student_dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        {% elif current_user.role.name in ['Industry Supervisor', 'School Supervisor'] %}
                            <a href="{{ url_for('supervisor_dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        {% elif current_user.role.name == 'ITF Supervisor' %}
                            <a href="{{ url_for('itf_dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        {% else %}
                            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-picture-large {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #dee2e6;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.profile-picture-preview {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #007bff;
}

.profile-picture-placeholder {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 4px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-bottom: none;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.list-unstyled li {
    padding: 0.25rem 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('profile_picture');
    const imagePreview = document.getElementById('imagePreview');
    const previewImage = document.getElementById('previewImage');
    const form = document.getElementById('profilePictureForm');

    // File input change handler
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        
        if (file) {
            // Validate file type
            const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/bmp', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                alert('Please select a valid image file (PNG, JPG, JPEG, GIF, BMP, WEBP).');
                fileInput.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Validate file size (50MB)
            if (file.size > 50 * 1024 * 1024) {
                alert('File size must be less than 50MB.');
                fileInput.value = '';
                imagePreview.style.display = 'none';
                return;
            }

            // Show preview
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
        }
    });

    // Form submission handler
    form.addEventListener('submit', function(e) {
        const file = fileInput.files[0];
        
        if (!file) {
            e.preventDefault();
            alert('Please select an image file to upload.');
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
        submitBtn.disabled = true;

        // Re-enable button after a delay (in case of errors)
        setTimeout(function() {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    });

    // Drag and drop functionality
    const cardBody = document.querySelector('.card-body');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        cardBody.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        cardBody.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        cardBody.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        cardBody.classList.add('drag-over');
    }

    function unhighlight(e) {
        cardBody.classList.remove('drag-over');
    }

    cardBody.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    }
});
</script>

<style>
.drag-over {
    background-color: #f8f9fa;
    border: 2px dashed #007bff;
}
</style>
{% endblock %}
