{% extends "base.html" %}

{% block title %}Monthly Comment - {{ student.user.name }} - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-alt me-2"></i>Monthly Comment - {{ student.user.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('supervisor_students') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Students
            </a>
        </div>
    </div>
</div>

<!-- Month Info -->
<div class="alert alert-info" role="alert">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Monthly Review for {{ current_month|month_name }} {{ current_year }}</strong><br>
    {% if existing_comment %}
        You have already provided a comment for this month. You can update it below.
    {% else %}
        Provide your monthly assessment of this student's performance based on their log entries.
    {% endif %}
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Monthly Comment Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comment-alt me-2"></i>Monthly Performance Comment
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="performance_rating" class="form-label">Performance Rating <span class="text-danger">*</span></label>
                        <select class="form-select" id="performance_rating" name="performance_rating" required>
                            <option value="">Select Rating</option>
                            <option value="Excellent" {% if existing_comment and existing_comment.performance_rating == 'Excellent' %}selected{% endif %}>
                                Excellent - Outstanding performance
                            </option>
                            <option value="Good" {% if existing_comment and existing_comment.performance_rating == 'Good' %}selected{% endif %}>
                                Good - Above average performance
                            </option>
                            <option value="Satisfactory" {% if existing_comment and existing_comment.performance_rating == 'Satisfactory' %}selected{% endif %}>
                                Satisfactory - Meets expectations
                            </option>
                            <option value="Needs Improvement" {% if existing_comment and existing_comment.performance_rating == 'Needs Improvement' %}selected{% endif %}>
                                Needs Improvement - Below expectations
                            </option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="comment" class="form-label">Monthly Comment <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="comment" name="comment" rows="8" required 
                                  placeholder="Provide detailed feedback on the student's performance this month, including strengths, areas for improvement, and overall progress...">{% if existing_comment %}{{ existing_comment.comment }}{% endif %}</textarea>
                        <div class="form-text">
                            Consider the student's consistency, quality of work, learning progress, and professional development.
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('supervisor_students') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if existing_comment %}Update Comment{% else %}Save Comment{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Student Info -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user me-2"></i>Student Information
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ student.user.name }}</p>
                <p><strong>Matric:</strong> {{ student.matric_number }}</p>
                <p><strong>Department:</strong> {{ student.department }}</p>
                <p><strong>Institution:</strong> {{ student.institution }}</p>
                <p><strong>Organization:</strong> {{ student.siwes_organization_name or 'Not specified' }}</p>
            </div>
        </div>

        <!-- Month Statistics -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>This Month's Activity
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h3 class="text-primary">{{ month_logs|length }}</h3>
                    <p class="text-muted">Log Entries</p>
                </div>
                
                {% if month_logs %}
                <div class="row text-center">
                    {% set approved_count = month_logs|selectattr('status', 'equalto', 'Approved')|list|length %}
                    {% set pending_count = month_logs|selectattr('status', 'equalto', 'Pending')|list|length %}
                    {% set rejected_count = month_logs|selectattr('status', 'equalto', 'Rejected')|list|length %}
                    
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success">{{ approved_count }}</h5>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-warning">{{ pending_count }}</h5>
                            <small class="text-muted">Pending</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-danger">{{ rejected_count }}</h5>
                        <small class="text-muted">Rejected</small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Previous Comments -->
        {% if existing_comment %}
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history me-2"></i>Current Comment
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Rating:</strong>
                    {% if existing_comment.performance_rating == 'Excellent' %}
                        <span class="badge bg-success">{{ existing_comment.performance_rating }}</span>
                    {% elif existing_comment.performance_rating == 'Good' %}
                        <span class="badge bg-info">{{ existing_comment.performance_rating }}</span>
                    {% elif existing_comment.performance_rating == 'Satisfactory' %}
                        <span class="badge bg-warning">{{ existing_comment.performance_rating }}</span>
                    {% else %}
                        <span class="badge bg-danger">{{ existing_comment.performance_rating }}</span>
                    {% endif %}
                </div>
                <div class="mb-2">
                    <strong>Date:</strong> {{ existing_comment.created_at.strftime('%Y-%m-%d %H:%M') }}
                </div>
                <div>
                    <strong>Comment:</strong><br>
                    <small class="text-muted">{{ existing_comment.comment[:100] }}{% if existing_comment.comment|length > 100 %}...{% endif %}</small>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Guidelines -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Comment Guidelines
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>Consider these aspects:</strong><br>
                    • Consistency in log submissions<br>
                    • Quality and detail of entries<br>
                    • Learning progress and growth<br>
                    • Professional behavior<br>
                    • Areas for improvement<br>
                    • Specific achievements<br>
                    • Future recommendations
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Recent Log Entries -->
{% if month_logs %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>This Month's Log Entries ({{ month_logs|length }})
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Activities</th>
                        <th>Status</th>
                        <th>Your Review</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in month_logs %}
                    <tr>
                        <td>{{ log.log_date.strftime('%m-%d') }}</td>
                        <td>
                            <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                                {{ log.activities_performed[:80] }}{% if log.activities_performed|length > 80 %}...{% endif %}
                            </div>
                        </td>
                        <td>
                            {% if log.status == 'Approved' %}
                                <span class="badge bg-success">{{ log.status }}</span>
                            {% elif log.status == 'Pending' %}
                                <span class="badge bg-warning">{{ log.status }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ log.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.school_review_comments %}
                                <small class="text-success">Reviewed</small>
                            {% else %}
                                <small class="text-muted">No review</small>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<script>
// Auto-resize textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('comment');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
        // Initial resize
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    }
});
</script>

<!-- Custom filter for month names -->
{% set month_names = {
    1: 'January', 2: 'February', 3: 'March', 4: 'April',
    5: 'May', 6: 'June', 7: 'July', 8: 'August',
    9: 'September', 10: 'October', 11: 'November', 12: 'December'
} %}

<script>
// Replace month number with name in the alert
document.addEventListener('DOMContentLoaded', function() {
    const monthNames = {
        1: 'January', 2: 'February', 3: 'March', 4: 'April',
        5: 'May', 6: 'June', 7: 'July', 8: 'August',
        9: 'September', 10: 'October', 11: 'November', 12: 'December'
    };
    
    const alertText = document.querySelector('.alert-info strong');
    if (alertText) {
        const text = alertText.textContent;
        const monthNum = {{ current_month }};
        const monthName = monthNames[monthNum];
        alertText.textContent = text.replace(/\d+/, monthName);
    }
});
</script>
{% endblock %}
