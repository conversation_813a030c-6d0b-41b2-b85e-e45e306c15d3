#!/usr/bin/env python3
"""
Admin User Creation Script for SIWES System
Creates the admin user if it doesn't exist
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Role
from werkzeug.security import generate_password_hash

def create_admin_user():
    """Create admin user if it doesn't exist"""
    print("🔄 Checking admin user...")
    
    with app.app_context():
        # Check if admin role exists
        admin_role = Role.query.filter_by(name='Admin').first()
        if not admin_role:
            print("❌ Admin role not found! Creating admin role...")
            admin_role = Role(name='Admin')
            db.session.add(admin_role)
            db.session.commit()
            print("✅ Admin role created")
        
        # Check if admin user exists
        admin_user = User.query.filter_by(email='<EMAIL>').first()
        
        if admin_user:
            print("ℹ️ Admin user already exists")
            print(f"   Email: {admin_user.email}")
            print(f"   Name: {admin_user.name}")
            print(f"   Role: {admin_user.role.name}")
            
            # Update password to ensure it's correct
            admin_user.password_hash = generate_password_hash('admin123')
            db.session.commit()
            print("✅ Admin password updated to: admin123")
            
        else:
            print("🔄 Creating admin user...")
            admin_user = User(
                name='System Administrator',
                email='<EMAIL>',
                phone='08000000000',
                password_hash=generate_password_hash('admin123'),
                role_id=admin_role.id,
                is_active=True
            )
            db.session.add(admin_user)
            db.session.commit()
            print("✅ Admin user created successfully!")
        
        print("\n🔑 ADMIN LOGIN CREDENTIALS:")
        print("   Email: <EMAIL>")
        print("   Password: admin123")
        print("   Role: System Administrator")
        
        # Verify login credentials
        test_user = User.query.filter_by(email='<EMAIL>').first()
        if test_user and test_user.check_password('admin123'):
            print("✅ Login credentials verified successfully!")
        else:
            print("❌ Login verification failed!")
        
        # Show all users for debugging
        print("\n📊 ALL USERS IN SYSTEM:")
        all_users = User.query.all()
        for user in all_users:
            print(f"   • {user.email} - {user.name} - {user.role.name}")

if __name__ == '__main__':
    create_admin_user()
