{% extends "base.html" %}

{% block title %}Manage Users - Admin Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users me-2"></i>Manage Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('assign_supervisor') }}">
                    <i class="fas fa-user-tie me-2"></i>Assign Supervisors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_system') }}">
                    <i class="fas fa-cog me-2"></i>System Settings
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>Manage Users
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-primary">{{ users.total }} Total Users</span>
        </div>
    </div>
</div>

<!-- User Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Users</h5>
                        <h2 class="mb-0">{{ users.total }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Students</h5>
                        <h2 class="mb-0">{{ users.items|selectattr('role.name', 'equalto', 'Student')|list|length }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-graduate fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Supervisors</h5>
                        <h2 class="mb-0">{{ users.items|selectattr('role.name', 'in', ['Industry Supervisor', 'School Supervisor'])|list|length }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Active Users</h5>
                        <h2 class="mb-0">{{ users.items|selectattr('is_active', 'equalto', true)|list|length }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if users.items %}
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>All Users
            </h5>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-secondary" onclick="filterUsers('all')">All</button>
                <button class="btn btn-outline-secondary" onclick="filterUsers('Student')">Students</button>
                <button class="btn btn-outline-secondary" onclick="filterUsers('Supervisor')">Supervisors</button>
                <button class="btn btn-outline-secondary" onclick="filterUsers('Admin')">Admins</button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped" id="usersTable">
                <thead>
                    <tr>
                        <th>User</th>
                        <th>Role</th>
                        <th>Contact</th>
                        <th>Status</th>
                        <th>Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users.items %}
                    <tr data-role="{{ user.role.name }}">
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-3">
                                    {{ user.name[0].upper() }}
                                </div>
                                <div>
                                    <strong>{{ user.name }}</strong><br>
                                    <small class="text-muted">ID: {{ user.id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'primary' if user.role.name == 'Student' else 'success' if 'Supervisor' in user.role.name else 'warning' if user.role.name == 'ITF Supervisor' else 'danger' }}">
                                {{ user.role.name }}
                            </span>
                        </td>
                        <td>
                            {{ user.email }}<br>
                            <small class="text-muted">{{ user.phone or 'No phone' }}</small>
                        </td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('admin_view_user', user_id=user.id) }}"
                                   class="btn btn-outline-info" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('admin_edit_user', user_id=user.id) }}"
                                   class="btn btn-outline-primary" title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.id != current_user.id %}
                                    {% if user.is_active %}
                                        <form method="POST" action="{{ url_for('admin_toggle_user_status', user_id=user.id) }}" style="display: inline;">
                                            <button type="submit" class="btn btn-outline-warning"
                                                    onclick="return confirm('Deactivate this user?')" title="Deactivate">
                                                <i class="fas fa-user-slash"></i>
                                            </button>
                                        </form>
                                    {% else %}
                                        <form method="POST" action="{{ url_for('admin_toggle_user_status', user_id=user.id) }}" style="display: inline;">
                                            <button type="submit" class="btn btn-outline-success"
                                                    onclick="return confirm('Activate this user?')" title="Activate">
                                                <i class="fas fa-user-check"></i>
                                            </button>
                                        </form>
                                    {% endif %}
                                    <form method="POST" action="{{ url_for('admin_delete_user', user_id=user.id) }}" style="display: inline;">
                                        <button type="submit" class="btn btn-outline-danger"
                                                onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone!')"
                                                title="Delete User">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if users.pages > 1 %}
<nav aria-label="Users pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if users.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin_users', page=users.prev_num) }}">Previous</a>
            </li>
        {% endif %}

        {% for page_num in users.iter_pages() %}
            {% if page_num %}
                {% if page_num != users.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin_users', page=page_num) }}">{{ page_num }}</a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}

        {% if users.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin_users', page=users.next_num) }}">Next</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-users fa-4x text-muted mb-4"></i>
        <h4 class="text-muted">No Users Found</h4>
        <p class="text-muted mb-4">No users have been registered in the system yet.</p>
    </div>
</div>
{% endif %}

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
</style>

<script>
function filterUsers(role) {
    const table = document.getElementById('usersTable');
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const userRole = row.getAttribute('data-role');

        if (role === 'all') {
            row.style.display = '';
        } else if (role === 'Supervisor' && (userRole.includes('Supervisor'))) {
            row.style.display = '';
        } else if (role === userRole) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }

    // Update active button
    const buttons = document.querySelectorAll('.btn-group .btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

// User management functions are now handled by server-side routes
</script>
{% endblock %}
