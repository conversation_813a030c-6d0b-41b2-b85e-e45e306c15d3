downloading the student report as a CSV file by the admin failed with this error:
AmbiguousForeignKeysError
sqlalchemy.exc.AmbiguousForeignKeysError: Can't determine join between 'students' and 'users'; tables have more than one foreign key constraint relationship between them. Please specify the 'onclause' of this join explicitly.

Traceback (most recent call last)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 1536, in __call__
return self.wsgi_app(environ, start_response)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 1514, in wsgi_app
response = self.handle_exception(e)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
response = self.full_dispatch_request()
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
rv = self.handle_user_exception(e)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
rv = self.dispatch_request()
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
return current_app.ensure_sync(func)(*args, **kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\app.py", line 2012, in export_report
return export_students_csv()
File "C:\Users\<USER>\Desktop\OPE\SWES\app.py", line 2037, in export_students_csv
students = Student.query.join(User).all()
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
return self._iter().all()  # type: ignore
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
return self._execute_internal(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
result: Result[Any] = compile_state_cls.orm_execute_statement(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
result = conn.execute(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
return meth(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
return connection._execute_clauseelement(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
compiled_sql = self._compiler(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
return dialect.statement_compiler(dialect, self, **kw)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
Compiled.__init__(self, dialect, statement, **kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
self.string = self.process(self.statement, **compile_kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
return obj._compiler_dispatch(self, **kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
return meth(self, **kw)  # type: ignore  # noqa: E501
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
compile_state = select_stmt._compile_state_factory(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
return klass.create_for_statement(statement, compiler, **kw)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
return cls._create_orm_context(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
self._setup_for_generate()
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
self._join(query._setup_joins, self._entities)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
self._join_left_to_right(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 2021, in _join_left_to_right
_ORMJoin(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\util.py", line 1884, in __init__
expression.Join.__init__(self, left, right, onclause, isouter, full)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1298, in __init__
self.onclause = self._match_primaries(self.left, self.right)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1409, in _match_primaries
return self._join_condition(left, right, a_subset=left_right)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1432, in _join_condition
cls._joincond_trim_constraints(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1577, in _joincond_trim_constraints
raise exc.AmbiguousForeignKeysError(
sqlalchemy.exc.AmbiguousForeignKeysError: Can't determine join between 'students' and 'users'; tables have more than one foreign key constraint relationship between them. Please specify the 'onclause' of this join explicitly.
The debugger caught an exception in your WSGI application. You can now look at the traceback which led to the error.
To switch between the interactive traceback and the plaintext one, you can click on the "Traceback" headline. From the text traceback you can also create a paste of it. For code execution mouse-over the frame you want to debug and click on the console icon on the right side.

You can execute arbitrary Python code in the stack frames and there are some extra helpers available for introspection:

dump() shows all variables in the frame
dump(obj) dumps all that's known about the object
Brought to you by DON'T PANIC, your friendly Werkzeug powered traceback interpreter.



downloading log entries report also failed with this error:
AmbiguousForeignKeysError
sqlalchemy.exc.AmbiguousForeignKeysError: Can't determine join between 'Join object on log_entries(1541935572896) and students(1541955331568)' and 'users'; tables have more than one foreign key constraint relationship between them. Please specify the 'onclause' of this join explicitly.

Traceback (most recent call last)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 1536, in __call__
return self.wsgi_app(environ, start_response)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 1514, in wsgi_app
response = self.handle_exception(e)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 1511, in wsgi_app
response = self.full_dispatch_request()Open an interactive python shell in this frame
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 919, in full_dispatch_request
rv = self.handle_user_exception(e)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 917, in full_dispatch_request
rv = self.dispatch_request()
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask\app.py", line 902, in dispatch_request
return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
return current_app.ensure_sync(func)(*args, **kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\app.py", line 2014, in export_report
return export_logs_csv()
File "C:\Users\<USER>\Desktop\OPE\SWES\app.py", line 2086, in export_logs_csv
logs = LogEntry.query.join(Student).join(User).all()
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
return self._iter().all()  # type: ignore
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
return self._execute_internal(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
result: Result[Any] = compile_state_cls.orm_execute_statement(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
result = conn.execute(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
return meth(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
return connection._execute_clauseelement(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1629, in _execute_clauseelement
compiled_sql, extracted_params, cache_hit = elem._compile_w_cache(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 711, in _compile_w_cache
compiled_sql = self._compiler(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 320, in _compiler
return dialect.statement_compiler(dialect, self, **kw)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 1446, in __init__
Compiled.__init__(self, dialect, statement, **kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 886, in __init__
self.string = self.process(self.statement, **compile_kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 932, in process
return obj._compiler_dispatch(self, **kwargs)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\visitors.py", line 141, in _compiler_dispatch
return meth(self, **kw)  # type: ignore  # noqa: E501
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\compiler.py", line 4728, in visit_select
compile_state = select_stmt._compile_state_factory(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\base.py", line 687, in create_for_statement
return klass.create_for_statement(statement, compiler, **kw)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 447, in create_for_statement
return cls._create_orm_context(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 1246, in _create_orm_context
self._setup_for_generate()
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 1281, in _setup_for_generate
self._join(query._setup_joins, self._entities)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 1922, in _join
self._join_left_to_right(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\context.py", line 1996, in _join_left_to_right
_ORMJoin(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\orm\util.py", line 1884, in __init__
expression.Join.__init__(self, left, right, onclause, isouter, full)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1298, in __init__
self.onclause = self._match_primaries(self.left, self.right)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1409, in _match_primaries
return self._join_condition(left, right, a_subset=left_right)
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1432, in _join_condition
cls._joincond_trim_constraints(
File "C:\Users\<USER>\Desktop\OPE\SWES\venv\lib\site-packages\sqlalchemy\sql\selectable.py", line 1577, in _joincond_trim_constraints
raise exc.AmbiguousForeignKeysError(
sqlalchemy.exc.AmbiguousForeignKeysError: Can't determine join between 'Join object on log_entries(1541935572896) and students(1541955331568)' and 'users'; tables have more than one foreign key constraint relationship between them. Please specify the 'onclause' of this join explicitly.
The debugger caught an exception in your WSGI application. You can now look at the traceback which led to the error.
To switch between the interactive traceback and the plaintext one, you can click on the "Traceback" headline. From the text traceback you can also create a paste of it. For code execution mouse-over the frame you want to debug and click on the console icon on the right side.

You can execute arbitrary Python code in the stack frames and there are some extra helpers available for introspection:

dump() shows all variables in the frame
dump(obj) dumps all that's known about the object
Brought to you by DON'T PANIC, your friendly Werkzeug powered traceback interpreter.


Log entries should be mostly industry reviewed and the Industry supervisor should append their signature weekly(at every end of the week) while the school supervisor should add signature monthly(any day within the lask week of the month)
The school supervisor just need to do some check once in a while to confirm the person is doing the SIWES internship.
The ITF supervisor should add signature(at the end of the logbook) after the end of the siwes program before exporting the student's logbook for printing. though the ITF cordinator can check the logbook to confirm the Student has made necessary entries and at least 60% of the Internship days is filled/logged.

In the event that we have more than one ITF cordinator in a state, the two can view the students in a state but only one should sign when there's need to sign(append signature) before logbook export, so the other can just be notified that the logbook is already signed by another if the logbook is already signed and allowed to sign if not.

ALso ensure that the supervisors(SCHOOL, INDUSTRY, ITF) can upload their signature and properly saved.

Test all improvements and ensure they are all working perfectly without errors.