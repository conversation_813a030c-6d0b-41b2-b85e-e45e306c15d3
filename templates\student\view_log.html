{% extends "base.html" %}

{% block title %}Log Entry - {{ log.log_date.strftime('%Y-%m-%d') }} - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_profile') }}">
                    <i class="fas fa-user me-2"></i>Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('new_log_entry') }}">
                    <i class="fas fa-plus me-2"></i>New Log Entry
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('view_school_supervisor') }}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>School Supervisor
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_logs') }}">
                    <i class="fas fa-list me-2"></i>View Logs
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-eye me-2"></i>Log Entry - {{ log.log_date.strftime('%B %d, %Y') }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('student_logs') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Logs
            </a>
            {% if log.status == 'Pending' %}
            <a href="{{ url_for('edit_log_entry', log_id=log.id) }}" class="btn btn-sm btn-primary">
                <i class="fas fa-edit me-1"></i>Edit
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>{{ log.log_date.strftime('%A, %B %d, %Y') }}
                    </h5>
                    {% if log.status == 'Pending' %}
                        <span class="badge bg-warning">{{ log.status }}</span>
                    {% elif log.status == 'Approved' %}
                        <span class="badge bg-success">{{ log.status }}</span>
                    {% elif log.status == 'Rejected' %}
                        <span class="badge bg-danger">{{ log.status }}</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ log.status }}</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <h6><i class="fas fa-tasks me-2"></i>Activities Performed</h6>
                <div class="bg-light p-3 rounded mb-4">
                    {{ log.activities_performed|replace('\n', '<br>')|safe }}
                </div>

                {% if log.key_learnings %}
                <h6><i class="fas fa-lightbulb me-2"></i>Key Learnings</h6>
                <div class="bg-light p-3 rounded mb-4">
                    {{ log.key_learnings|replace('\n', '<br>')|safe }}
                </div>
                {% endif %}

                <!-- Media Files Section -->
                {% if log.media_files %}
                <h6><i class="fas fa-paperclip me-2"></i>Attached Media Files</h6>
                <div class="row mb-4">
                    {% for media in log.media_files %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-2">
                                    {% if media.file_type == 'image' %}
                                        <i class="fas fa-image fa-2x text-primary me-3"></i>
                                    {% elif media.file_type == 'video' %}
                                        <i class="fas fa-video fa-2x text-success me-3"></i>
                                    {% elif media.file_type == 'document' %}
                                        <i class="fas fa-file-alt fa-2x text-info me-3"></i>
                                    {% else %}
                                        <i class="fas fa-file fa-2x text-secondary me-3"></i>
                                    {% endif %}
                                    <div class="flex-grow-1">
                                        <h6 class="mb-0">{{ media.original_filename }}</h6>
                                        <small class="text-muted">
                                            {{ (media.file_size / 1024 / 1024)|round(2) }} MB
                                        </small>
                                    </div>
                                </div>

                                <!-- Media Preview/Display -->
                                {% if media.file_type == 'image' %}
                                    <div class="mb-2">
                                        <img src="{{ url_for('uploaded_file', filename=media.filename) }}"
                                             class="img-fluid rounded"
                                             style="max-height: 200px; width: 100%; object-fit: cover;"
                                             alt="{{ media.original_filename }}">
                                    </div>
                                {% elif media.file_type == 'video' %}
                                    <div class="mb-2">
                                        <video controls class="w-100" style="max-height: 200px;">
                                            <source src="{{ url_for('uploaded_file', filename=media.filename) }}"
                                                    type="{{ media.mime_type }}">
                                            Your browser does not support the video tag.
                                        </video>
                                    </div>
                                {% endif %}

                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="{{ url_for('uploaded_file', filename=media.filename) }}"
                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                    <a href="{{ url_for('uploaded_file', filename=media.filename) }}"
                                       class="btn btn-sm btn-outline-secondary" download="{{ media.original_filename }}">
                                        <i class="fas fa-download me-1"></i>Download
                                    </a>
                                    {% if log.status == 'Pending' %}
                                    <form method="POST" action="{{ url_for('delete_media_file', log_id=log.id, media_id=media.id) }}"
                                          style="display: inline;" onsubmit="return confirm('Delete this file?')">
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash me-1"></i>Delete
                                        </button>
                                    </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Supervisor Reviews -->
                {% if log.industry_review_comments or log.school_review_comments %}
                <h6><i class="fas fa-comments me-2"></i>Supervisor Reviews</h6>

                {% if log.industry_review_comments %}
                <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-industry me-2"></i>Industry Supervisor Review
                            {% if log.industry_review_date %}
                                <small class="float-end">{{ log.industry_review_date.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% endif %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {{ log.industry_review_comments|replace('\n', '<br>')|safe }}
                    </div>
                </div>
                {% endif %}

                {% if log.school_review_comments %}
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-school me-2"></i>School Supervisor Review
                            {% if log.school_review_date %}
                                <small class="float-end">{{ log.school_review_date.strftime('%Y-%m-%d %H:%M') }}</small>
                            {% endif %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {{ log.school_review_comments|replace('\n', '<br>')|safe }}
                    </div>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Entry Details
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Date:</strong> {{ log.log_date.strftime('%Y-%m-%d') }}</p>
                <p><strong>Day:</strong> {{ log.log_date.strftime('%A') }}</p>
                <p><strong>Status:</strong>
                    {% if log.status == 'Pending' %}
                        <span class="badge bg-warning">{{ log.status }}</span>
                    {% elif log.status == 'Approved' %}
                        <span class="badge bg-success">{{ log.status }}</span>
                    {% elif log.status == 'Rejected' %}
                        <span class="badge bg-danger">{{ log.status }}</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ log.status }}</span>
                    {% endif %}
                </p>
                <p><strong>Created:</strong> {{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</p>

                {% if log.status == 'Pending' %}
                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-clock me-2"></i>
                        This entry is awaiting review by your supervisors.
                    </small>
                </div>
                {% elif log.status == 'Approved' %}
                <div class="alert alert-success">
                    <small>
                        <i class="fas fa-check-circle me-2"></i>
                        This entry has been approved by your supervisors.
                    </small>
                </div>
                {% elif log.status == 'Rejected' %}
                <div class="alert alert-danger">
                    <small>
                        <i class="fas fa-times-circle me-2"></i>
                        This entry needs revision. Please check supervisor comments.
                    </small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Student Info
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ current_user.name }}</p>
                <p><strong>Matric Number:</strong> {{ student.matric_number }}</p>
                <p><strong>Organization:</strong> {{ student.siwes_organization_name or 'Not set' }}</p>
            </div>
        </div>

        {% if log.status == 'Pending' %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('edit_log_entry', log_id=log.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Entry
                    </a>
                </div>
                <div class="alert alert-warning mt-3">
                    <small>
                        <strong>Note:</strong> You can only edit entries that haven't been reviewed yet.
                    </small>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
