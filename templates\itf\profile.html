{% extends "base.html" %}

{% block title %}ITF Supervisor Profile - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_students') }}">
                    <i class="fas fa-users me-2"></i>Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_analytics') }}">
                    <i class="fas fa-chart-line me-2"></i>Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('itf_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>ITF Supervisor Profile
    </h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-flag me-2"></i>State Assignment Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="assigned_state" class="form-label">Assigned State <span class="text-danger">*</span></label>
                        <select class="form-select" id="assigned_state" name="assigned_state" required>
                            <option value="">Select State</option>
                            <option value="Abia" {{ 'selected' if profile and profile.assigned_state == 'Abia' else '' }}>Abia</option>
                            <option value="Adamawa" {{ 'selected' if profile and profile.assigned_state == 'Adamawa' else '' }}>Adamawa</option>
                            <option value="Akwa Ibom" {{ 'selected' if profile and profile.assigned_state == 'Akwa Ibom' else '' }}>Akwa Ibom</option>
                            <option value="Anambra" {{ 'selected' if profile and profile.assigned_state == 'Anambra' else '' }}>Anambra</option>
                            <option value="Bauchi" {{ 'selected' if profile and profile.assigned_state == 'Bauchi' else '' }}>Bauchi</option>
                            <option value="Bayelsa" {{ 'selected' if profile and profile.assigned_state == 'Bayelsa' else '' }}>Bayelsa</option>
                            <option value="Benue" {{ 'selected' if profile and profile.assigned_state == 'Benue' else '' }}>Benue</option>
                            <option value="Borno" {{ 'selected' if profile and profile.assigned_state == 'Borno' else '' }}>Borno</option>
                            <option value="Cross River" {{ 'selected' if profile and profile.assigned_state == 'Cross River' else '' }}>Cross River</option>
                            <option value="Delta" {{ 'selected' if profile and profile.assigned_state == 'Delta' else '' }}>Delta</option>
                            <option value="Ebonyi" {{ 'selected' if profile and profile.assigned_state == 'Ebonyi' else '' }}>Ebonyi</option>
                            <option value="Edo" {{ 'selected' if profile and profile.assigned_state == 'Edo' else '' }}>Edo</option>
                            <option value="Ekiti" {{ 'selected' if profile and profile.assigned_state == 'Ekiti' else '' }}>Ekiti</option>
                            <option value="Enugu" {{ 'selected' if profile and profile.assigned_state == 'Enugu' else '' }}>Enugu</option>
                            <option value="FCT" {{ 'selected' if profile and profile.assigned_state == 'FCT' else '' }}>Federal Capital Territory</option>
                            <option value="Gombe" {{ 'selected' if profile and profile.assigned_state == 'Gombe' else '' }}>Gombe</option>
                            <option value="Imo" {{ 'selected' if profile and profile.assigned_state == 'Imo' else '' }}>Imo</option>
                            <option value="Jigawa" {{ 'selected' if profile and profile.assigned_state == 'Jigawa' else '' }}>Jigawa</option>
                            <option value="Kaduna" {{ 'selected' if profile and profile.assigned_state == 'Kaduna' else '' }}>Kaduna</option>
                            <option value="Kano" {{ 'selected' if profile and profile.assigned_state == 'Kano' else '' }}>Kano</option>
                            <option value="Katsina" {{ 'selected' if profile and profile.assigned_state == 'Katsina' else '' }}>Katsina</option>
                            <option value="Kebbi" {{ 'selected' if profile and profile.assigned_state == 'Kebbi' else '' }}>Kebbi</option>
                            <option value="Kogi" {{ 'selected' if profile and profile.assigned_state == 'Kogi' else '' }}>Kogi</option>
                            <option value="Kwara" {{ 'selected' if profile and profile.assigned_state == 'Kwara' else '' }}>Kwara</option>
                            <option value="Lagos" {{ 'selected' if profile and profile.assigned_state == 'Lagos' else '' }}>Lagos</option>
                            <option value="Nasarawa" {{ 'selected' if profile and profile.assigned_state == 'Nasarawa' else '' }}>Nasarawa</option>
                            <option value="Niger" {{ 'selected' if profile and profile.assigned_state == 'Niger' else '' }}>Niger</option>
                            <option value="Ogun" {{ 'selected' if profile and profile.assigned_state == 'Ogun' else '' }}>Ogun</option>
                            <option value="Ondo" {{ 'selected' if profile and profile.assigned_state == 'Ondo' else '' }}>Ondo</option>
                            <option value="Osun" {{ 'selected' if profile and profile.assigned_state == 'Osun' else '' }}>Osun</option>
                            <option value="Oyo" {{ 'selected' if profile and profile.assigned_state == 'Oyo' else '' }}>Oyo</option>
                            <option value="Plateau" {{ 'selected' if profile and profile.assigned_state == 'Plateau' else '' }}>Plateau</option>
                            <option value="Rivers" {{ 'selected' if profile and profile.assigned_state == 'Rivers' else '' }}>Rivers</option>
                            <option value="Sokoto" {{ 'selected' if profile and profile.assigned_state == 'Sokoto' else '' }}>Sokoto</option>
                            <option value="Taraba" {{ 'selected' if profile and profile.assigned_state == 'Taraba' else '' }}>Taraba</option>
                            <option value="Yobe" {{ 'selected' if profile and profile.assigned_state == 'Yobe' else '' }}>Yobe</option>
                            <option value="Zamfara" {{ 'selected' if profile and profile.assigned_state == 'Zamfara' else '' }}>Zamfara</option>
                        </select>
                        <div class="form-text">Select the state you are responsible for supervising</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">Position</label>
                            <select class="form-select" id="position" name="position">
                                <option value="">Select Position</option>
                                <option value="ITF Officer" {{ 'selected' if profile and profile.position == 'ITF Officer' else '' }}>ITF Officer</option>
                                <option value="Senior ITF Officer" {{ 'selected' if profile and profile.position == 'Senior ITF Officer' else '' }}>Senior ITF Officer</option>
                                <option value="Principal ITF Officer" {{ 'selected' if profile and profile.position == 'Principal ITF Officer' else '' }}>Principal ITF Officer</option>
                                <option value="Assistant Director" {{ 'selected' if profile and profile.position == 'Assistant Director' else '' }}>Assistant Director</option>
                                <option value="Deputy Director" {{ 'selected' if profile and profile.position == 'Deputy Director' else '' }}>Deputy Director</option>
                                <option value="Director" {{ 'selected' if profile and profile.position == 'Director' else '' }}>Director</option>
                                <option value="State Coordinator" {{ 'selected' if profile and profile.position == 'State Coordinator' else '' }}>State Coordinator</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="years_of_experience" class="form-label">Years of Experience</label>
                            <input type="number" class="form-control" id="years_of_experience" name="years_of_experience" 
                                   value="{{ profile.years_of_experience if profile else '' }}" min="0" max="50">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="office_location" class="form-label">Office Location</label>
                        <input type="text" class="form-control" id="office_location" name="office_location" 
                               value="{{ profile.office_location if profile else '' }}"
                               placeholder="e.g., ITF Area Office, Lagos">
                    </div>

                    <div class="mb-3">
                        <label for="specialization" class="form-label">Area of Specialization</label>
                        <input type="text" class="form-control" id="specialization" name="specialization"
                               value="{{ profile.specialization if profile else '' }}"
                               placeholder="e.g., Skills Development, Industrial Training">
                    </div>

                    <!-- Digital Signature Upload -->
                    <div class="mb-3">
                        <label for="signature_file" class="form-label">Digital Signature</label>
                        <input type="file" class="form-control" id="signature_file" name="signature_file"
                               accept=".png,.jpg,.jpeg,.gif,.bmp,.webp">
                        <div class="form-text">Upload your digital signature (PNG, JPG, JPEG, GIF, BMP, WEBP only)</div>
                        {% if profile and profile.signature_filename %}
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>Current signature: {{ profile.signature_filename }}
                            </small>
                            <div class="mt-2">
                                <img src="{{ url_for('uploaded_signature', filename=profile.signature_filename) }}"
                                     alt="Current Signature" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('itf_dashboard') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Account Information
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ current_user.name }}</p>
                <p><strong>Email:</strong> {{ current_user.email }}</p>
                <p><strong>Phone:</strong> {{ current_user.phone or 'Not provided' }}</p>
                <p><strong>Role:</strong> {{ current_user.role.name }}</p>
                <p><strong>Member Since:</strong> {{ current_user.created_at.strftime('%B %d, %Y') }}</p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Profile Guidelines
                </h5>
            </div>
            <div class="card-body">
                <h6>Required Information</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Assigned state</li>
                </ul>

                <h6 class="mt-3">Supervision Scope</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-users text-primary me-2"></i>Monitor students in your state</li>
                    <li><i class="fas fa-users text-primary me-2"></i>Oversee SIWES implementation</li>
                    <li><i class="fas fa-users text-primary me-2"></i>Generate state reports</li>
                </ul>

                <div class="alert alert-info mt-3">
                    <small>
                        <strong>Note:</strong> You will only have access to students conducting SIWES in your assigned state.
                    </small>
                </div>
            </div>
        </div>

        {% if profile %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Assignment Status
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h6 class="text-success">Profile Complete</h6>
                    <p class="text-muted">You are supervising students in:</p>
                    <div class="alert alert-success">
                        <strong>{{ profile.assigned_state }}</strong><br>
                        <small>{{ profile.position or 'ITF Supervisor' }}</small>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Profile Status
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h6 class="text-warning">Profile Incomplete</h6>
                    <p class="text-muted">Complete your profile to access state-specific student data.</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
