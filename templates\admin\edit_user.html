{% extends "base.html" %}

{% block title %}Edit User - {{ user.name }} - Admin Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users me-2"></i>Manage Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('assign_supervisor') }}">
                    <i class="fas fa-user-tie me-2"></i>Assign Supervisors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_workload') }}">
                    <i class="fas fa-chart-bar me-2"></i>Supervisor Workload
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_system') }}">
                    <i class="fas fa-cog me-2"></i>System Settings
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>Edit User - {{ user.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin_view_user', user_id=user.id) }}" class="btn btn-outline-info">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
        </div>
        <div class="btn-group">
            <a href="{{ url_for('admin_users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>Edit User Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ user.name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ user.phone or '' }}" placeholder="e.g., 08012345678">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role_id" class="form-label">User Role *</label>
                                <select class="form-select" id="role_id" name="role_id" required>
                                    {% for role in roles %}
                                    <option value="{{ role.id }}" {{ 'selected' if role.id == user.role_id else '' }}>
                                        {{ role.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="Leave blank to keep current password">
                                <div class="form-text">
                                    Only enter a password if you want to change it. Minimum 6 characters.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Account Status</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {{ 'checked' if user.is_active else '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Account is active
                                    </label>
                                </div>
                                <div class="form-text">
                                    Inactive users cannot log in to the system.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> Changing the user's role may affect their access to certain features. 
                        Profile information specific to their current role will be preserved.
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Changes
                            </button>
                            <a href="{{ url_for('admin_view_user', user_id=user.id) }}" class="btn btn-outline-secondary ms-2">
                                Cancel
                            </a>
                        </div>
                        
                        {% if user.id != current_user.id %}
                        <div class="btn-group">
                            {% if user.is_active %}
                                <form method="POST" action="{{ url_for('admin_toggle_user_status', user_id=user.id) }}" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-warning" 
                                            onclick="return confirm('Deactivate this user?')">
                                        <i class="fas fa-user-slash me-2"></i>Deactivate
                                    </button>
                                </form>
                            {% else %}
                                <form method="POST" action="{{ url_for('admin_toggle_user_status', user_id=user.id) }}" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-success" 
                                            onclick="return confirm('Activate this user?')">
                                        <i class="fas fa-user-check me-2"></i>Activate
                                    </button>
                                </form>
                            {% endif %}
                            
                            <form method="POST" action="{{ url_for('admin_delete_user', user_id=user.id) }}" style="display: inline;">
                                <button type="submit" class="btn btn-outline-danger ms-2" 
                                        onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone!')">
                                    <i class="fas fa-trash me-2"></i>Delete User
                                </button>
                            </form>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Current User Info -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Current Information
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-circle-large mx-auto mb-3">
                        {{ user.name[0].upper() }}
                    </div>
                    <h6>{{ user.name }}</h6>
                    <span class="badge bg-{{ 'primary' if user.role.name == 'Student' else 'success' if 'Supervisor' in user.role.name else 'warning' if user.role.name == 'ITF Supervisor' else 'danger' }}">
                        {{ user.role.name }}
                    </span>
                </div>
                
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>User ID:</strong></td>
                        <td>{{ user.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td class="text-break">{{ user.email }}</td>
                    </tr>
                    <tr>
                        <td><strong>Phone:</strong></td>
                        <td>{{ user.phone or 'Not provided' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Registered:</strong></td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <!-- Edit Guidelines -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Edit Guidelines
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Name and email are required fields</li>
                    <li><i class="fas fa-check text-success me-2"></i>Email must be unique in the system</li>
                    <li><i class="fas fa-check text-success me-2"></i>Password must be at least 6 characters</li>
                    <li><i class="fas fa-check text-success me-2"></i>Role changes take effect immediately</li>
                    <li><i class="fas fa-check text-success me-2"></i>Inactive users cannot log in</li>
                </ul>
                
                <div class="alert alert-warning mt-3">
                    <small>
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> You cannot deactivate or delete your own account.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
}
</style>

<script>
// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const passwordField = document.getElementById('password');
    
    form.addEventListener('submit', function(e) {
        const password = passwordField.value;
        
        if (password && password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long.');
            passwordField.focus();
            return false;
        }
    });
    
    // Role change warning
    const roleSelect = document.getElementById('role_id');
    const originalRole = roleSelect.value;
    
    roleSelect.addEventListener('change', function() {
        if (this.value !== originalRole) {
            if (!confirm('Changing the user role may affect their access to certain features. Continue?')) {
                this.value = originalRole;
            }
        }
    });
});
</script>
{% endblock %}
