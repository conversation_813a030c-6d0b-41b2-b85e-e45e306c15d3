{% extends "base.html" %}

{% block title %}{{ student.user.name }} - Student Details - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-graduate me-2"></i>{{ student.user.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('supervisor_students') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Students
            </a>
        </div>
    </div>
</div>

<!-- Student Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Logs</h5>
                        <h2 class="mb-0">{{ total_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Approved</h5>
                        <h2 class="mb-0">{{ approved_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Pending</h5>
                        <h2 class="mb-0">{{ pending_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Rejected</h5>
                        <h2 class="mb-0">{{ rejected_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Log Entries
                </h5>
            </div>
            <div class="card-body">
                {% if logs %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Activities</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>
                                        <strong>{{ log.log_date.strftime('%Y-%m-%d') }}</strong><br>
                                        <small class="text-muted">{{ log.log_date.strftime('%A') }}</small>
                                    </td>
                                    <td>
                                        {{ log.activities_performed[:60] }}
                                        {% if log.activities_performed|length > 60 %}...{% endif %}
                                    </td>
                                    <td>
                                        {% if log.status == 'Pending' %}
                                            <span class="badge bg-warning">{{ log.status }}</span>
                                        {% elif log.status == 'Approved' %}
                                            <span class="badge bg-success">{{ log.status }}</span>
                                        {% elif log.status == 'Rejected' %}
                                            <span class="badge bg-danger">{{ log.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.status == 'Pending' %}
                                            <a href="{{ url_for('review_log_entry', log_id=log.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye me-1"></i>Review
                                            </a>
                                        {% else %}
                                            <button class="btn btn-sm btn-outline-secondary" disabled>
                                                <i class="fas fa-check me-1"></i>Reviewed
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-book fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No log entries yet</h5>
                        <p class="text-muted">This student hasn't submitted any log entries.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Student Information
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ student.user.name }}</p>
                <p><strong>Email:</strong> {{ student.user.email }}</p>
                <p><strong>Phone:</strong> {{ student.user.phone or 'Not provided' }}</p>
                <p><strong>Matric Number:</strong> {{ student.matric_number }}</p>
                <p><strong>Department:</strong> {{ student.department or 'Not specified' }}</p>
                <p><strong>Institution:</strong> {{ student.institution or 'Not specified' }}</p>
                <p><strong>Registered:</strong> {{ student.created_at.strftime('%Y-%m-%d') }}</p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>SIWES Details
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Organization:</strong> {{ student.siwes_organization_name or 'Not specified' }}</p>
                {% if student.siwes_organization_address %}
                <p><strong>Address:</strong><br>{{ student.siwes_organization_address }}</p>
                {% endif %}
                {% if student.siwes_start_date and student.siwes_end_date %}
                <p><strong>Period:</strong><br>
                {{ student.siwes_start_date.strftime('%Y-%m-%d') }} to 
                {{ student.siwes_end_date.strftime('%Y-%m-%d') }}</p>
                
                <!-- Calculate duration -->
                {% set duration = (student.siwes_end_date - student.siwes_start_date).days %}
                <p><strong>Duration:</strong> {{ duration }} days</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Progress Overview
                </h5>
            </div>
            <div class="card-body">
                {% if total_logs > 0 %}
                <div class="mb-3">
                    <label class="form-label">Approval Rate</label>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ (approved_logs / total_logs * 100)|round(1) }}%">
                            {{ (approved_logs / total_logs * 100)|round(1) }}%
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Status Distribution</label>
                    <div class="progress">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ (approved_logs / total_logs * 100)|round(1) }}%">
                        </div>
                        <div class="progress-bar bg-warning" role="progressbar" 
                             style="width: {{ (pending_logs / total_logs * 100)|round(1) }}%">
                        </div>
                        <div class="progress-bar bg-danger" role="progressbar" 
                             style="width: {{ (rejected_logs / total_logs * 100)|round(1) }}%">
                        </div>
                    </div>
                    <small class="text-muted">
                        <span class="badge bg-success">{{ approved_logs }}</span>
                        <span class="badge bg-warning">{{ pending_logs }}</span>
                        <span class="badge bg-danger">{{ rejected_logs }}</span>
                    </small>
                </div>
                {% else %}
                <p class="text-muted">No data available yet.</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Supervision Team
                </h5>
            </div>
            <div class="card-body">
                {% if student.industry_supervisor %}
                <p><strong>Industry Supervisor:</strong><br>
                {{ student.industry_supervisor.name }}
                {% if student.industry_supervisor_id == current_user.id %}
                    <span class="badge bg-primary">You</span>
                {% endif %}
                </p>
                {% endif %}
                
                {% if student.school_supervisor %}
                <p><strong>School Supervisor:</strong><br>
                {{ student.school_supervisor.name }}
                {% if student.school_supervisor_id == current_user.id %}
                    <span class="badge bg-primary">You</span>
                {% endif %}
                </p>
                {% endif %}
                
                {% if not student.industry_supervisor and not student.school_supervisor %}
                <p class="text-muted">No supervisors assigned yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
