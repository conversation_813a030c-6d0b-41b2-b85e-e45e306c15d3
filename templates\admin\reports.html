{% extends "base.html" %}

{% block title %}System Reports - Admin Dashboard - SIWES Reporting System{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-chart-bar me-2"></i>System Reports
                    </h2>
                    <p class="text-muted">Comprehensive system analytics and data exports</p>
                </div>
                <div>
                    <a href="{{ url_for('admin_dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Overview Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Users</h5>
                            <h2 class="mb-0">{{ total_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Students</h5>
                            <h2 class="mb-0">{{ total_students }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-graduate fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Supervisors</h5>
                            <h2 class="mb-0">{{ total_supervisors }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chalkboard-teacher fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Log Entries</h5>
                            <h2 class="mb-0">{{ total_logs }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Log Status Overview -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                    <h5>{{ approved_logs }}</h5>
                    <p class="text-muted">Approved Logs</p>
                    {% if total_logs > 0 %}
                        <small class="text-success">{{ "%.1f"|format((approved_logs / total_logs) * 100) }}% approval rate</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                    <h5>{{ pending_logs }}</h5>
                    <p class="text-muted">Pending Review</p>
                    {% if total_logs > 0 %}
                        <small class="text-warning">{{ "%.1f"|format((pending_logs / total_logs) * 100) }}% pending</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x text-danger mb-3"></i>
                    <h5>{{ rejected_logs }}</h5>
                    <p class="text-muted">Rejected Logs</p>
                    {% if total_logs > 0 %}
                        <small class="text-danger">{{ "%.1f"|format((rejected_logs / total_logs) * 100) }}% rejected</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Supervisor Assignment Statistics -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-industry fa-2x text-primary mb-3"></i>
                    <h5>{{ students_with_industry }}</h5>
                    <p class="text-muted">Students with Industry Supervisor</p>
                    {% if total_students > 0 %}
                        <small class="text-primary">{{ "%.1f"|format((students_with_industry / total_students) * 100) }}% assigned</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-university fa-2x text-success mb-3"></i>
                    <h5>{{ students_with_school }}</h5>
                    <p class="text-muted">Students with School Supervisor</p>
                    {% if total_students > 0 %}
                        <small class="text-success">{{ "%.1f"|format((students_with_school / total_students) * 100) }}% assigned</small>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <i class="fas fa-check-double fa-2x text-info mb-3"></i>
                    <h5>{{ fully_assigned_students }}</h5>
                    <p class="text-muted">Fully Assigned Students</p>
                    {% if total_students > 0 %}
                        <small class="text-info">{{ "%.1f"|format((fully_assigned_students / total_students) * 100) }}% complete</small>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Log Status Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Log Status Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="logStatusChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Department Distribution Chart -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Students by Department
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="departmentChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Export Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-download me-2"></i>Data Export Options
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-graduate fa-2x text-primary mb-2"></i>
                                    <h6>Students Report</h6>
                                    <p class="small text-muted">Complete student data with log statistics</p>
                                    <a href="{{ url_for('export_report', report_type='students_csv') }}"
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i>Download CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                                    <h6>Log Entries Report</h6>
                                    <p class="small text-muted">All log entries with review status</p>
                                    <a href="{{ url_for('export_report', report_type='logs_csv') }}"
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-download me-1"></i>Download CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-chalkboard-teacher fa-2x text-info mb-2"></i>
                                    <h6>Supervisors Report</h6>
                                    <p class="small text-muted">Supervisor details and workload data</p>
                                    <a href="{{ url_for('export_report', report_type='supervisors_csv') }}"
                                       class="btn btn-sm btn-info">
                                        <i class="fas fa-download me-1"></i>Download CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                                    <h6>System Summary</h6>
                                    <p class="small text-muted">Key metrics and statistics overview</p>
                                    <a href="{{ url_for('export_report', report_type='system_summary_csv') }}"
                                       class="btn btn-sm btn-warning">
                                        <i class="fas fa-download me-1"></i>Download CSV
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics Tables -->
    <div class="row mb-4">
        <!-- Department Statistics -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Students by Department
                    </h5>
                </div>
                <div class="card-body">
                    {% if dept_stats %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Department</th>
                                        <th class="text-end">Students</th>
                                        <th class="text-end">Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dept, count in dept_stats %}
                                    <tr>
                                        <td>{{ dept or 'Not Specified' }}</td>
                                        <td class="text-end">{{ count }}</td>
                                        <td class="text-end">
                                            {% if total_students > 0 %}
                                                {{ "%.1f"|format((count / total_students) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No department data available</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Institution Statistics -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-university me-2"></i>Students by Institution
                    </h5>
                </div>
                <div class="card-body">
                    {% if inst_stats %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Institution</th>
                                        <th class="text-end">Students</th>
                                        <th class="text-end">Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for inst, count in inst_stats %}
                                    <tr>
                                        <td>{{ inst or 'Not Specified' }}</td>
                                        <td class="text-end">{{ count }}</td>
                                        <td class="text-end">
                                            {% if total_students > 0 %}
                                                {{ "%.1f"|format((count / total_students) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No institution data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mb-4">
        <!-- Recent Users -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Recent Registrations
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_users %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Role</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in recent_users %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if user.profile_picture %}
                                                    <img src="{{ url_for('uploaded_profile', filename=user.profile_picture) }}"
                                                         alt="Profile" class="avatar-circle-small me-2">
                                                {% else %}
                                                    <div class="avatar-circle-small me-2">
                                                        {{ user.name[0].upper() }}
                                                    </div>
                                                {% endif %}
                                                <span>{{ user.name }}</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'primary' if user.role.name == 'Student' else 'success' if 'Supervisor' in user.role.name else 'warning' if user.role.name == 'ITF Supervisor' else 'danger' }}">
                                                {{ user.role.name }}
                                            </span>
                                        </td>
                                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No recent registrations</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Log Entries -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Recent Log Entries
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_logs %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in recent_logs %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if log.student.user.profile_picture %}
                                                    <img src="{{ url_for('uploaded_profile', filename=log.student.user.profile_picture) }}"
                                                         alt="Profile" class="avatar-circle-small me-2">
                                                {% else %}
                                                    <div class="avatar-circle-small me-2">
                                                        {{ log.student.user.name[0].upper() }}
                                                    </div>
                                                {% endif %}
                                                <span>{{ log.student.user.name }}</span>
                                            </div>
                                        </td>
                                        <td>{{ log.log_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if log.status == 'Approved' else 'warning' if log.status == 'Pending' else 'danger' }}">
                                                {{ log.status }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No recent log entries</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.avatar-circle-small {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.7rem;
    object-fit: cover;
    border: 1px solid #dee2e6;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Log Status Chart
{% if total_logs > 0 %}
const logStatusCtx = document.getElementById('logStatusChart').getContext('2d');
const logStatusChart = new Chart(logStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Approved', 'Pending', 'Rejected'],
        datasets: [{
            data: [{{ approved_logs }}, {{ pending_logs }}, {{ rejected_logs }}],
            backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// Department Chart
{% if dept_stats %}
const deptCtx = document.getElementById('departmentChart').getContext('2d');
const departmentChart = new Chart(deptCtx, {
    type: 'bar',
    data: {
        labels: [{% for dept, count in dept_stats %}'{{ dept or "Not Specified" }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: 'Students',
            data: [{% for dept, count in dept_stats %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
