{% extends "base.html" %}

{% block title %}Student Profile - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('student_profile') }}">
                    <i class="fas fa-user me-2"></i>Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('new_log_entry') }}">
                    <i class="fas fa-plus me-2"></i>New Log Entry
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('view_school_supervisor') }}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>School Supervisor
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_logs') }}">
                    <i class="fas fa-list me-2"></i>View Logs
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>Student Profile
    </h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Profile Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="matric_number" class="form-label">Matriculation Number</label>
                            <input type="text" class="form-control" id="matric_number" name="matric_number"
                                   value="{{ student.matric_number if student else '' }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">Department</label>
                            <input type="text" class="form-control" id="department" name="department"
                                   value="{{ student.department if student else '' }}">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="institution" class="form-label">Institution</label>
                        <input type="text" class="form-control" id="institution" name="institution"
                               value="{{ student.institution if student else '' }}">
                    </div>

                    <div class="mb-3">
                        <label for="siwes_organization_name" class="form-label">SIWES Organization Name</label>
                        <input type="text" class="form-control" id="siwes_organization_name" name="siwes_organization_name"
                               value="{{ student.siwes_organization_name if student else '' }}">
                    </div>

                    <div class="mb-3">
                        <label for="siwes_organization_address" class="form-label">Organization Address</label>
                        <textarea class="form-control" id="siwes_organization_address" name="siwes_organization_address" rows="3">{{ student.siwes_organization_address if student else '' }}</textarea>
                    </div>

                    <div class="mb-3">
                        <label for="siwes_state" class="form-label">SIWES State <span class="text-danger">*</span></label>
                        <select class="form-select" id="siwes_state" name="siwes_state" required>
                            <option value="">Select State</option>
                            <option value="Abia" {{ 'selected' if student and student.siwes_state == 'Abia' else '' }}>Abia</option>
                            <option value="Adamawa" {{ 'selected' if student and student.siwes_state == 'Adamawa' else '' }}>Adamawa</option>
                            <option value="Akwa Ibom" {{ 'selected' if student and student.siwes_state == 'Akwa Ibom' else '' }}>Akwa Ibom</option>
                            <option value="Anambra" {{ 'selected' if student and student.siwes_state == 'Anambra' else '' }}>Anambra</option>
                            <option value="Bauchi" {{ 'selected' if student and student.siwes_state == 'Bauchi' else '' }}>Bauchi</option>
                            <option value="Bayelsa" {{ 'selected' if student and student.siwes_state == 'Bayelsa' else '' }}>Bayelsa</option>
                            <option value="Benue" {{ 'selected' if student and student.siwes_state == 'Benue' else '' }}>Benue</option>
                            <option value="Borno" {{ 'selected' if student and student.siwes_state == 'Borno' else '' }}>Borno</option>
                            <option value="Cross River" {{ 'selected' if student and student.siwes_state == 'Cross River' else '' }}>Cross River</option>
                            <option value="Delta" {{ 'selected' if student and student.siwes_state == 'Delta' else '' }}>Delta</option>
                            <option value="Ebonyi" {{ 'selected' if student and student.siwes_state == 'Ebonyi' else '' }}>Ebonyi</option>
                            <option value="Edo" {{ 'selected' if student and student.siwes_state == 'Edo' else '' }}>Edo</option>
                            <option value="Ekiti" {{ 'selected' if student and student.siwes_state == 'Ekiti' else '' }}>Ekiti</option>
                            <option value="Enugu" {{ 'selected' if student and student.siwes_state == 'Enugu' else '' }}>Enugu</option>
                            <option value="FCT" {{ 'selected' if student and student.siwes_state == 'FCT' else '' }}>Federal Capital Territory</option>
                            <option value="Gombe" {{ 'selected' if student and student.siwes_state == 'Gombe' else '' }}>Gombe</option>
                            <option value="Imo" {{ 'selected' if student and student.siwes_state == 'Imo' else '' }}>Imo</option>
                            <option value="Jigawa" {{ 'selected' if student and student.siwes_state == 'Jigawa' else '' }}>Jigawa</option>
                            <option value="Kaduna" {{ 'selected' if student and student.siwes_state == 'Kaduna' else '' }}>Kaduna</option>
                            <option value="Kano" {{ 'selected' if student and student.siwes_state == 'Kano' else '' }}>Kano</option>
                            <option value="Katsina" {{ 'selected' if student and student.siwes_state == 'Katsina' else '' }}>Katsina</option>
                            <option value="Kebbi" {{ 'selected' if student and student.siwes_state == 'Kebbi' else '' }}>Kebbi</option>
                            <option value="Kogi" {{ 'selected' if student and student.siwes_state == 'Kogi' else '' }}>Kogi</option>
                            <option value="Kwara" {{ 'selected' if student and student.siwes_state == 'Kwara' else '' }}>Kwara</option>
                            <option value="Lagos" {{ 'selected' if student and student.siwes_state == 'Lagos' else '' }}>Lagos</option>
                            <option value="Nasarawa" {{ 'selected' if student and student.siwes_state == 'Nasarawa' else '' }}>Nasarawa</option>
                            <option value="Niger" {{ 'selected' if student and student.siwes_state == 'Niger' else '' }}>Niger</option>
                            <option value="Ogun" {{ 'selected' if student and student.siwes_state == 'Ogun' else '' }}>Ogun</option>
                            <option value="Ondo" {{ 'selected' if student and student.siwes_state == 'Ondo' else '' }}>Ondo</option>
                            <option value="Osun" {{ 'selected' if student and student.siwes_state == 'Osun' else '' }}>Osun</option>
                            <option value="Oyo" {{ 'selected' if student and student.siwes_state == 'Oyo' else '' }}>Oyo</option>
                            <option value="Plateau" {{ 'selected' if student and student.siwes_state == 'Plateau' else '' }}>Plateau</option>
                            <option value="Rivers" {{ 'selected' if student and student.siwes_state == 'Rivers' else '' }}>Rivers</option>
                            <option value="Sokoto" {{ 'selected' if student and student.siwes_state == 'Sokoto' else '' }}>Sokoto</option>
                            <option value="Taraba" {{ 'selected' if student and student.siwes_state == 'Taraba' else '' }}>Taraba</option>
                            <option value="Yobe" {{ 'selected' if student and student.siwes_state == 'Yobe' else '' }}>Yobe</option>
                            <option value="Zamfara" {{ 'selected' if student and student.siwes_state == 'Zamfara' else '' }}>Zamfara</option>
                        </select>
                        <div class="form-text">Select the state where you are conducting your SIWES</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="siwes_start_date" class="form-label">SIWES Start Date</label>
                            <input type="date" class="form-control" id="siwes_start_date" name="siwes_start_date"
                                   value="{{ student.siwes_start_date.strftime('%Y-%m-%d') if student and student.siwes_start_date else '' }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="siwes_end_date" class="form-label">SIWES End Date</label>
                            <input type="date" class="form-control" id="siwes_end_date" name="siwes_end_date"
                                   value="{{ student.siwes_end_date.strftime('%Y-%m-%d') if student and student.siwes_end_date else '' }}">
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('student_dashboard') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Account Information
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ current_user.name }}</p>
                <p><strong>Email:</strong> {{ current_user.email }}</p>
                <p><strong>Phone:</strong> {{ current_user.phone or 'Not provided' }}</p>
                <p><strong>Role:</strong> {{ current_user.role.name }}</p>
                <p><strong>Member Since:</strong> {{ current_user.created_at.strftime('%B %d, %Y') }}</p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Profile Tips
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Complete all required fields</li>
                    <li><i class="fas fa-check text-success me-2"></i>Provide accurate organization details</li>
                    <li><i class="fas fa-check text-success me-2"></i>Set correct SIWES dates</li>
                    <li><i class="fas fa-check text-success me-2"></i>Keep information up to date</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
