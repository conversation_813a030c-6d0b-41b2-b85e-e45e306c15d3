{% extends "base.html" %}

{% block title %}My Log Entries - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_profile') }}">
                    <i class="fas fa-user me-2"></i>Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('new_log_entry') }}">
                    <i class="fas fa-plus me-2"></i>New Log Entry
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('view_school_supervisor') }}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>School Supervisor
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('student_logs') }}">
                    <i class="fas fa-list me-2"></i>View Logs
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-list me-2"></i>My Log Entries
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('new_log_entry') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus me-1"></i>New Log Entry
            </a>
        </div>
    </div>
</div>

{% if logs.items %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Activities Performed</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs.items %}
                    <tr>
                        <td>
                            <strong>{{ log.log_date.strftime('%Y-%m-%d') }}</strong><br>
                            <small class="text-muted">{{ log.log_date.strftime('%A') }}</small>
                        </td>
                        <td>
                            {{ log.activities_performed[:100] }}
                            {% if log.activities_performed|length > 100 %}...{% endif %}
                            {% if log.media_files %}
                                <br><small class="text-muted">
                                    <i class="fas fa-paperclip me-1"></i>{{ log.media_files|length }} file{{ 's' if log.media_files|length != 1 else '' }}
                                </small>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.status == 'Pending' %}
                                <span class="badge bg-warning">{{ log.status }}</span>
                            {% elif log.status == 'Approved' %}
                                <span class="badge bg-success">{{ log.status }}</span>
                            {% elif log.status == 'Rejected' %}
                                <span class="badge bg-danger">{{ log.status }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ log.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_log_entry', log_id=log.id) }}" class="btn btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if log.status in ['Pending', 'Rejected'] %}
                                <a href="{{ url_for('edit_log_entry', log_id=log.id) }}" class="btn btn-outline-secondary"
                                   title="{% if log.status == 'Rejected' %}Edit & Resubmit{% else %}Edit{% endif %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if logs.pages > 1 %}
<nav aria-label="Log entries pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if logs.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('student_logs', page=logs.prev_num) }}">Previous</a>
            </li>
        {% endif %}

        {% for page_num in logs.iter_pages() %}
            {% if page_num %}
                {% if page_num != logs.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('student_logs', page=page_num) }}">{{ page_num }}</a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}

        {% if logs.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('student_logs', page=logs.next_num) }}">Next</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-book fa-4x text-muted mb-4"></i>
        <h4 class="text-muted">No Log Entries Yet</h4>
        <p class="text-muted mb-4">Start documenting your SIWES experience by creating your first log entry.</p>
        <a href="{{ url_for('new_log_entry') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create Your First Log Entry
        </a>
    </div>
</div>
{% endif %}

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-info-circle fa-2x text-info mb-3"></i>
                <h6>Tips for Log Entries</h6>
                <ul class="list-unstyled text-start">
                    <li>• Be specific about activities</li>
                    <li>• Include key learnings</li>
                    <li>• Submit entries daily</li>
                    <li>• Review before submitting</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-pie fa-2x text-success mb-3"></i>
                <h6>Status Legend</h6>
                <div class="text-start">
                    <span class="badge bg-warning me-2">Pending</span> Awaiting review<br>
                    <span class="badge bg-success me-2">Approved</span> Reviewed and approved<br>
                    <span class="badge bg-danger me-2">Rejected</span> Needs revision
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                <h6>Quick Stats</h6>
                <p class="mb-1"><strong>Total Entries:</strong> {{ logs.total }}</p>
                <p class="mb-1"><strong>This Page:</strong> {{ logs.items|length }}</p>
                <p class="mb-0"><strong>Pages:</strong> {{ logs.pages }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
