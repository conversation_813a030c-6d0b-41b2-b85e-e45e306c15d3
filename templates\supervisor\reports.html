{% extends "base.html" %}

{% block title %}{{ supervisor_type }} Reports - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-bar me-2"></i>{{ supervisor_type }} Reports
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('supervisor_dashboard') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">My Students</h5>
                        <h2 class="mb-0">{{ total_students }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Logs</h5>
                        <h2 class="mb-0">{{ total_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Approved</h5>
                        <h2 class="mb-0">{{ approved_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Pending</h5>
                        <h2 class="mb-0">{{ pending_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Log Entry Status Distribution
                </h5>
            </div>
            <div class="card-body">
                {% if total_logs > 0 %}
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="statusChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-md-6">
                        <h6>Status Breakdown</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span><i class="fas fa-circle text-success me-2"></i>Approved</span>
                                <span>{{ approved_logs }} ({{ (approved_logs / total_logs * 100)|round(1) }}%)</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-success" style="width: {{ (approved_logs / total_logs * 100)|round(1) }}%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span><i class="fas fa-circle text-warning me-2"></i>Pending</span>
                                <span>{{ pending_logs }} ({{ (pending_logs / total_logs * 100)|round(1) }}%)</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-warning" style="width: {{ (pending_logs / total_logs * 100)|round(1) }}%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span><i class="fas fa-circle text-danger me-2"></i>Rejected</span>
                                <span>{{ rejected_logs }} ({{ (rejected_logs / total_logs * 100)|round(1) }}%)</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar bg-danger" style="width: {{ (rejected_logs / total_logs * 100)|round(1) }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Data Available</h5>
                    <p class="text-muted">No log entries have been submitted by your students yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-{{ 'building' if supervisor_type == 'Industry Supervisor' else 'university' }} me-2"></i>
                    {% if supervisor_type == 'Industry Supervisor' %}Organizations{% else %}Departments{% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if dept_stats %}
                    {% for name, count in dept_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ name or 'Not Specified' }}</span>
                        <span class="badge bg-primary">{{ count }}</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No data available.</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Activity
                </h5>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                    {% for log in recent_logs %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small class="text-muted">{{ log.student.user.name }}</small><br>
                            <small>{{ log.log_date.strftime('%Y-%m-%d') }}</small>
                        </div>
                        <span class="badge bg-{{ 'success' if log.status == 'Approved' else 'warning' if log.status == 'Pending' else 'danger' }}">
                            {{ log.status }}
                        </span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No recent activity.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>Export Options
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-user-graduate fa-2x text-primary mb-2"></i>
                                <h6>Students Report</h6>
                                <p class="small text-muted">My assigned students with log statistics</p>
                                <a href="{{ url_for('supervisor_export_report', report_type='students_csv') }}"
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-download me-1"></i>Download CSV
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-file-alt fa-2x text-success mb-2"></i>
                                <h6>Log Entries Report</h6>
                                <p class="small text-muted">All log entries from my students</p>
                                <a href="{{ url_for('supervisor_export_report', report_type='logs_csv') }}"
                                   class="btn btn-sm btn-success">
                                    <i class="fas fa-download me-1"></i>Download CSV
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-2x text-info mb-2"></i>
                                <h6>Summary Report</h6>
                                <p class="small text-muted">My supervision statistics and metrics</p>
                                <a href="{{ url_for('supervisor_export_report', report_type='summary_csv') }}"
                                   class="btn btn-sm btn-info">
                                    <i class="fas fa-download me-1"></i>Download CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Status Distribution Chart
{% if total_logs > 0 %}
const ctx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Approved', 'Pending', 'Rejected'],
        datasets: [{
            data: [{{ approved_logs }}, {{ pending_logs }}, {{ rejected_logs }}],
            backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
