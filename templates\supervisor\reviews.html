{% extends "base.html" %}

{% block title %}Pending Reviews - {{ supervisor_type }} - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-warning">{{ pending_logs|length }} Pending</span>
        </div>
    </div>
</div>

{% if pending_logs %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Student</th>
                        <th>Date</th>
                        <th>Activities</th>
                        <th>Submitted</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in pending_logs %}
                    <tr>
                        <td>
                            <strong>{{ log.student.user.name }}</strong><br>
                            <small class="text-muted">{{ log.student.matric_number }}</small>
                        </td>
                        <td>
                            <strong>{{ log.log_date.strftime('%Y-%m-%d') }}</strong><br>
                            <small class="text-muted">{{ log.log_date.strftime('%A') }}</small>
                        </td>
                        <td>
                            {{ log.activities_performed[:80] }}
                            {% if log.activities_performed|length > 80 %}...{% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('review_log_entry', log_id=log.id) }}" class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i>Review
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                <h5>{{ pending_logs|length }}</h5>
                <p class="text-muted">Total Pending Reviews</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x text-info mb-3"></i>
                {% set today_logs = pending_logs|selectattr('log_date', 'equalto', today())|list %}
                <h5>{{ today_logs|length }}</h5>
                <p class="text-muted">Due Today</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                {% set overdue_logs = pending_logs|selectattr('log_date', 'lt', today())|list %}
                <h5>{{ overdue_logs|length }}</h5>
                <p class="text-muted">Overdue</p>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-check-circle fa-4x text-success mb-4"></i>
        <h4 class="text-success">All Caught Up!</h4>
        <p class="text-muted mb-4">
            There are no pending log entries to review at this time.
        </p>
        <div class="alert alert-success">
            <strong>Great job!</strong> You've reviewed all submitted log entries.
            New submissions will appear here for your review.
        </div>
        <a href="{{ url_for('supervisor_students') }}" class="btn btn-primary">
            <i class="fas fa-users me-2"></i>View My Students
        </a>
    </div>
</div>
{% endif %}

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Review Guidelines
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>What to Look For</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Detailed activity descriptions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Clear learning outcomes</li>
                            <li><i class="fas fa-check text-success me-2"></i>Professional language</li>
                            <li><i class="fas fa-check text-success me-2"></i>Relevant skill development</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Feedback Tips</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Be specific and constructive</li>
                            <li><i class="fas fa-check text-success me-2"></i>Highlight strengths</li>
                            <li><i class="fas fa-check text-success me-2"></i>Suggest improvements</li>
                            <li><i class="fas fa-check text-success me-2"></i>Encourage reflection</li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-3">
                    <strong>Review Process:</strong>
                    <ol class="mb-0">
                        <li>Click "Review" to view the full log entry</li>
                        <li>Read the activities and learnings carefully</li>
                        <li>Provide constructive feedback in comments</li>
                        <li>Choose to approve or request revisions</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
