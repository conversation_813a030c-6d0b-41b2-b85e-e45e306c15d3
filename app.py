from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, date, timedelta
import os
import uuid
import io
import csv
from flask import make_response
from datetime import timedelta

app = Flask(__name__)
app.config['SECRET_KEY'] = 'siwes-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:@localhost/siwes_python'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# File upload configuration
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['PROFILE_UPLOAD_FOLDER'] = 'uploads/profiles'
app.config['SIGNATURE_UPLOAD_FOLDER'] = 'uploads/signatures'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'pdf', 'doc', 'docx'}
PROFILE_ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
SIGNATURE_ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

# Create upload directories if they don't exist
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])
if not os.path.exists(app.config['PROFILE_UPLOAD_FOLDER']):
    os.makedirs(app.config['PROFILE_UPLOAD_FOLDER'])
if not os.path.exists(app.config['SIGNATURE_UPLOAD_FOLDER']):
    os.makedirs(app.config['SIGNATURE_UPLOAD_FOLDER'])

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Template helper functions
@app.template_global()
def days_since(target_date):
    """Calculate days since a given date"""
    if isinstance(target_date, datetime):
        target_date = target_date.date()
    today = date.today()
    return (today - target_date).days

@app.template_global()
def today():
    """Get today's date"""
    return date.today()

@app.template_global()
def timedelta_obj(days=0, seconds=0, microseconds=0, milliseconds=0, minutes=0, hours=0, weeks=0):
    """Get a timedelta object for template use"""
    return timedelta(days=days, seconds=seconds, microseconds=microseconds,
                    milliseconds=milliseconds, minutes=minutes, hours=hours, weeks=weeks)

@app.template_global()
def count_logs_by_status(students, status):
    """Count total log entries with specific status across all students"""
    total = 0
    for student in students:
        total += len([log for log in student.log_entries if log.status == status])
    return total

@app.template_global()
def count_students_with_supervisors(students):
    """Count students who have at least one supervisor (industry or school)"""
    count = 0
    for student in students:
        if hasattr(student, 'industry_supervisor') and hasattr(student, 'school_supervisor'):
            if student.industry_supervisor or student.school_supervisor:
                count += 1
        elif hasattr(student, 'industry_supervisor_id') and hasattr(student, 'school_supervisor_id'):
            if student.industry_supervisor_id or student.school_supervisor_id:
                count += 1
    return count
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'info'

# Database Models
class Role(db.Model):
    __tablename__ = 'roles'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(200))
    users = db.relationship('User', backref='role', lazy=True)

    def __repr__(self):
        return f'<Role {self.name}>'

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    password_hash = db.Column(db.String(255), nullable=False)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    profile_picture = db.Column(db.String(255))  # Filename of profile picture
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.email}>'

class Student(db.Model):
    __tablename__ = 'students'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    matric_number = db.Column(db.String(50), unique=True, nullable=False)
    department = db.Column(db.String(100))
    institution = db.Column(db.String(200))
    industry_supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    school_supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    siwes_organization_name = db.Column(db.String(200))
    siwes_organization_address = db.Column(db.Text)
    siwes_state = db.Column(db.String(50))  # State where SIWES is taking place
    siwes_start_date = db.Column(db.Date)
    siwes_end_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', foreign_keys=[user_id], backref='student_profile')
    industry_supervisor = db.relationship('User', foreign_keys=[industry_supervisor_id])
    school_supervisor = db.relationship('User', foreign_keys=[school_supervisor_id])

class SchoolSupervisor(db.Model):
    __tablename__ = 'school_supervisors'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    institution = db.Column(db.String(200), nullable=False)
    department = db.Column(db.String(100), nullable=False)
    faculty = db.Column(db.String(150))
    position = db.Column(db.String(100))  # Lecturer, Professor, etc.
    office_location = db.Column(db.String(200))
    specialization = db.Column(db.String(200))
    years_of_experience = db.Column(db.Integer)
    signature_filename = db.Column(db.String(255))  # Digital signature file
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='school_supervisor_profile')

class IndustrySupervisor(db.Model):
    __tablename__ = 'industry_supervisors'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    company_name = db.Column(db.String(200), nullable=False)
    industry_type = db.Column(db.String(100), nullable=False)  # IT, Engineering, Finance, etc.
    position = db.Column(db.String(100), nullable=False)  # Manager, Engineer, etc.
    department = db.Column(db.String(100))  # Company department
    company_address = db.Column(db.Text)
    company_size = db.Column(db.String(50))  # Small, Medium, Large, Enterprise
    years_of_experience = db.Column(db.Integer)
    specialization = db.Column(db.String(200))
    signature_filename = db.Column(db.String(255))  # Digital signature file
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='industry_supervisor_profile')

class ITFSupervisor(db.Model):
    __tablename__ = 'itf_supervisors'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_state = db.Column(db.String(50), nullable=False)  # State they supervise
    office_location = db.Column(db.String(200))
    position = db.Column(db.String(100))  # ITF Officer, Senior Officer, etc.
    years_of_experience = db.Column(db.Integer)
    specialization = db.Column(db.String(200))
    signature_filename = db.Column(db.String(255))  # Digital signature file
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='itf_supervisor_profile')

class LogEntry(db.Model):
    __tablename__ = 'log_entries'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    log_date = db.Column(db.Date, nullable=False)
    activities_performed = db.Column(db.Text, nullable=False)
    key_learnings = db.Column(db.Text)
    status = db.Column(db.String(50), default='Pending')
    industry_review_comments = db.Column(db.Text)
    industry_review_date = db.Column(db.DateTime)
    school_review_comments = db.Column(db.Text)
    school_review_date = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    student = db.relationship('Student', backref='log_entries')

class MediaFile(db.Model):
    __tablename__ = 'media_files'
    id = db.Column(db.Integer, primary_key=True)
    log_entry_id = db.Column(db.Integer, db.ForeignKey('log_entries.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50), nullable=False)  # image, video, document
    file_size = db.Column(db.Integer, nullable=False)  # in bytes
    mime_type = db.Column(db.String(100), nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)

    log_entry = db.relationship('LogEntry', backref='media_files')

class MonthlyComment(db.Model):
    __tablename__ = 'monthly_comments'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    school_supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    month = db.Column(db.Integer, nullable=False)  # 1-12
    year = db.Column(db.Integer, nullable=False)
    comment = db.Column(db.Text, nullable=False)
    performance_rating = db.Column(db.String(20))  # Excellent, Good, Satisfactory, Needs Improvement
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    student = db.relationship('Student', backref='monthly_comments')
    school_supervisor = db.relationship('User', backref='monthly_comments_given')

class WeeklySignature(db.Model):
    __tablename__ = 'weekly_signatures'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    industry_supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    week_start_date = db.Column(db.Date, nullable=False)
    week_end_date = db.Column(db.Date, nullable=False)
    signature_date = db.Column(db.DateTime, default=datetime.utcnow)
    comments = db.Column(db.Text)

    student = db.relationship('Student', backref='weekly_signatures')
    industry_supervisor = db.relationship('User', backref='weekly_signatures_given')

class MonthlySignature(db.Model):
    __tablename__ = 'monthly_signatures'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    school_supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    month = db.Column(db.Integer, nullable=False)  # 1-12
    year = db.Column(db.Integer, nullable=False)
    signature_date = db.Column(db.DateTime, default=datetime.utcnow)
    comments = db.Column(db.Text)

    student = db.relationship('Student', backref='monthly_signatures')
    school_supervisor = db.relationship('User', backref='monthly_signatures_given')

class FinalSignature(db.Model):
    __tablename__ = 'final_signatures'
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('students.id'), nullable=False)
    itf_supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    signature_date = db.Column(db.DateTime, default=datetime.utcnow)
    comments = db.Column(db.Text)
    logbook_completion_percentage = db.Column(db.Float)  # Percentage of logbook completion

    student = db.relationship('Student', backref='final_signatures')
    itf_supervisor = db.relationship('User', backref='final_signatures_given')

# Helper functions for file handling
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_type(filename):
    ext = filename.rsplit('.', 1)[1].lower()
    if ext in {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}:
        return 'image'
    elif ext in {'mp4', 'avi', 'mov', 'wmv', 'flv'}:
        return 'video'
    elif ext in {'pdf', 'doc', 'docx'}:
        return 'document'
    else:
        return 'other'

def generate_unique_filename(original_filename):
    ext = original_filename.rsplit('.', 1)[1].lower()
    unique_id = str(uuid.uuid4())
    return f"{unique_id}.{ext}"

def allowed_profile_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in PROFILE_ALLOWED_EXTENSIONS

def allowed_signature_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in SIGNATURE_ALLOWED_EXTENSIONS

def get_profile_picture_url(user):
    """Get the URL for a user's profile picture or return default"""
    if user.profile_picture:
        return url_for('uploaded_profile', filename=user.profile_picture)
    else:
        # Return default avatar based on user's first letter
        return None

def save_profile_picture(file, user_id):
    """Save uploaded profile picture and return filename"""
    if file and allowed_profile_file(file.filename):
        # Generate unique filename
        filename = secure_filename(file.filename)
        name, ext = os.path.splitext(filename)
        unique_filename = f"profile_{user_id}_{uuid.uuid4().hex[:8]}{ext}"

        # Save file
        file_path = os.path.join(app.config['PROFILE_UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        return unique_filename
    return None

def save_signature_file(file, user_id, supervisor_type):
    """Save signature file and return filename"""
    if file and allowed_signature_file(file.filename):
        # Generate unique filename
        filename = secure_filename(file.filename)
        name, ext = os.path.splitext(filename)
        unique_filename = f"signature_{supervisor_type}_{user_id}_{uuid.uuid4().hex[:8]}{ext}"

        # Save file
        file_path = os.path.join(app.config['SIGNATURE_UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        return unique_filename
    return None

def delete_profile_picture(filename):
    """Delete profile picture file"""
    if filename:
        file_path = os.path.join(app.config['PROFILE_UPLOAD_FOLDER'], filename)
        if os.path.exists(file_path):
            os.remove(file_path)

# Signature timing helper functions
def get_weekly_signature_status(student_id, week_start_date):
    """Check if weekly signature exists for a specific week"""
    return WeeklySignature.query.filter_by(
        student_id=student_id,
        week_start_date=week_start_date
    ).first()

def get_monthly_signature_status(student_id, month, year):
    """Check if monthly signature exists for a specific month"""
    return MonthlySignature.query.filter_by(
        student_id=student_id,
        month=month,
        year=year
    ).first()

def get_final_signature_status(student_id):
    """Check if final signature exists for a student"""
    return FinalSignature.query.filter_by(student_id=student_id).first()

def can_itf_supervisor_sign(student_id, itf_supervisor_id):
    """Check if ITF supervisor can sign (no other ITF supervisor has signed)"""
    existing_signature = FinalSignature.query.filter_by(student_id=student_id).first()

    if not existing_signature:
        return True, "Can sign"

    if existing_signature.itf_supervisor_id == itf_supervisor_id:
        return False, "Already signed by you"

    # Get the other ITF supervisor's name
    other_supervisor = User.query.get(existing_signature.itf_supervisor_id)
    return False, f"Already signed by {other_supervisor.name if other_supervisor else 'another ITF supervisor'}"

def get_logbook_completion_percentage(student_id):
    """Calculate logbook completion percentage"""
    student = Student.query.get(student_id)
    if not student or not student.siwes_start_date or not student.siwes_end_date:
        return 0

    total_days = (student.siwes_end_date - student.siwes_start_date).days + 1
    logged_days = LogEntry.query.filter_by(student_id=student_id).count()

    return (logged_days / total_days * 100) if total_days > 0 else 0

# Automatic Assignment Functions
def get_supervisor_workload(supervisor_id, supervisor_type):
    """Calculate current workload for a supervisor"""
    if supervisor_type == 'school':
        return Student.query.filter_by(school_supervisor_id=supervisor_id).count()
    elif supervisor_type == 'industry':
        return Student.query.filter_by(industry_supervisor_id=supervisor_id).count()
    return 0

def find_least_loaded_school_supervisor(institution, department):
    """Find the least loaded school supervisor for given institution and department"""
    # Get all school supervisors with matching profiles
    matching_supervisors = db.session.query(User, SchoolSupervisor)\
        .join(SchoolSupervisor, User.id == SchoolSupervisor.user_id)\
        .filter(
            SchoolSupervisor.institution == institution,
            SchoolSupervisor.department == department,
            User.is_active == True
        ).all()

    if not matching_supervisors:
        return None

    # Calculate workload for each supervisor and find the least loaded
    least_loaded = None
    min_workload = float('inf')

    for user, profile in matching_supervisors:
        workload = get_supervisor_workload(user.id, 'school')
        if workload < min_workload:
            min_workload = workload
            least_loaded = user

    return least_loaded

def find_least_loaded_industry_supervisor(industry_type=None):
    """Find the least loaded industry supervisor, optionally filtered by industry type"""
    # Get all industry supervisors with profiles
    query = db.session.query(User, IndustrySupervisor)\
        .join(IndustrySupervisor, User.id == IndustrySupervisor.user_id)\
        .filter(User.is_active == True)

    if industry_type:
        query = query.filter(IndustrySupervisor.industry_type == industry_type)

    matching_supervisors = query.all()

    if not matching_supervisors:
        return None

    # Calculate workload for each supervisor and find the least loaded
    least_loaded = None
    min_workload = float('inf')

    for user, profile in matching_supervisors:
        workload = get_supervisor_workload(user.id, 'industry')
        if workload < min_workload:
            min_workload = workload
            least_loaded = user

    return least_loaded

def auto_assign_supervisors(student):
    """Automatically assign supervisors to a student using least loaded algorithm"""
    assignment_results = {
        'school_supervisor': None,
        'industry_supervisor': None,
        'school_assigned': False,
        'industry_assigned': False,
        'messages': []
    }

    # Auto-assign school supervisor
    if student.institution and student.department:
        school_supervisor = find_least_loaded_school_supervisor(
            student.institution,
            student.department
        )

        if school_supervisor:
            student.school_supervisor_id = school_supervisor.id
            assignment_results['school_supervisor'] = school_supervisor
            assignment_results['school_assigned'] = True
            assignment_results['messages'].append(
                f"School supervisor assigned: {school_supervisor.name} from {student.institution}"
            )
        else:
            assignment_results['messages'].append(
                f"No available school supervisor found for {student.institution}, {student.department}"
            )

    # Auto-assign industry supervisor (try to match by industry type if available)
    industry_supervisor = find_least_loaded_industry_supervisor()

    if industry_supervisor:
        student.industry_supervisor_id = industry_supervisor.id
        assignment_results['industry_supervisor'] = industry_supervisor
        assignment_results['industry_assigned'] = True
        assignment_results['messages'].append(
            f"Industry supervisor assigned: {industry_supervisor.name}"
        )
    else:
        assignment_results['messages'].append(
            "No available industry supervisor found"
        )

    return assignment_results

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        name = request.form['name']
        email = request.form['email']
        phone = request.form.get('phone', '')
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        role_id = request.form['role_id']

        # Validation
        if password != confirm_password:
            flash('Passwords do not match!', 'error')
            return redirect(url_for('register'))

        if len(password) < 6:
            flash('Password must be at least 6 characters long!', 'error')
            return redirect(url_for('register'))

        # Check if user already exists
        if User.query.filter_by(email=email).first():
            flash('Email already registered!', 'error')
            return redirect(url_for('register'))

        # Create new user
        password_hash = generate_password_hash(password)
        user = User(name=name, email=email, phone=phone,
                   password_hash=password_hash, role_id=role_id)

        db.session.add(user)
        db.session.commit()

        flash('Registration successful! Please login.', 'success')
        return redirect(url_for('login'))

    roles = Role.query.all()
    return render_template('register.html', roles=roles)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']

        user = User.query.filter_by(email=email).first()

        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            flash(f'Welcome back, {user.name}!', 'success')

            # Redirect based on role
            if user.role.name == 'Student':
                return redirect(url_for('student_dashboard'))
            elif user.role.name == 'Industry Supervisor':
                return redirect(url_for('supervisor_dashboard'))
            elif user.role.name == 'School Supervisor':
                return redirect(url_for('supervisor_dashboard'))
            elif user.role.name == 'ITF Supervisor':
                return redirect(url_for('itf_dashboard'))
            else:
                return redirect(url_for('admin_dashboard'))
        else:
            flash('Invalid email or password!', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

@app.route('/student/dashboard')
@login_required
def student_dashboard():
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()
    if not student:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('student_profile'))

    recent_logs = LogEntry.query.filter_by(student_id=student.id).order_by(LogEntry.log_date.desc()).limit(5).all()
    total_logs = LogEntry.query.filter_by(student_id=student.id).count()
    approved_logs = LogEntry.query.filter_by(student_id=student.id, status='Approved').count()
    pending_logs = LogEntry.query.filter_by(student_id=student.id, status='Pending').count()

    return render_template('student/dashboard.html',
                         student=student,
                         recent_logs=recent_logs,
                         total_logs=total_logs,
                         approved_logs=approved_logs,
                         pending_logs=pending_logs)

@app.route('/supervisor/dashboard')
@login_required
def supervisor_dashboard():
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get students assigned to this supervisor
    if current_user.role.name == 'Industry Supervisor':
        students = Student.query.filter_by(industry_supervisor_id=current_user.id).all()
    else:
        students = Student.query.filter_by(school_supervisor_id=current_user.id).all()

    # Get pending log entries for review
    pending_logs = []
    for student in students:
        logs = LogEntry.query.filter_by(student_id=student.id, status='Pending').all()
        pending_logs.extend(logs)

    return render_template('supervisor/dashboard.html',
                         students=students,
                         pending_logs=pending_logs,
                         supervisor_type=current_user.role.name)

@app.route('/itf/dashboard')
@login_required
def itf_dashboard():
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get system statistics
    total_students = Student.query.count()
    total_logs = LogEntry.query.count()
    pending_logs = LogEntry.query.filter_by(status='Pending').count()
    approved_logs = LogEntry.query.filter_by(status='Approved').count()

    recent_students = Student.query.order_by(Student.created_at.desc()).limit(5).all()

    return render_template('itf/dashboard.html',
                         total_students=total_students,
                         total_logs=total_logs,
                         pending_logs=pending_logs,
                         approved_logs=approved_logs,
                         recent_students=recent_students)

@app.route('/admin/dashboard')
@login_required
def admin_dashboard():
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get system statistics
    total_users = User.query.count()
    total_students = Student.query.count()
    total_supervisors = User.query.filter(User.role_id.in_([2, 3, 4])).count()
    total_logs = LogEntry.query.count()

    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()

    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_students=total_students,
                         total_supervisors=total_supervisors,
                         total_logs=total_logs,
                         recent_users=recent_users)

@app.route('/student/profile', methods=['GET', 'POST'])
@login_required
def student_profile():
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()

    if request.method == 'POST':
        matric_number = request.form['matric_number']
        department = request.form['department']
        institution = request.form['institution']
        siwes_organization_name = request.form['siwes_organization_name']
        siwes_organization_address = request.form['siwes_organization_address']
        siwes_state = request.form['siwes_state']
        siwes_start_date = datetime.strptime(request.form['siwes_start_date'], '%Y-%m-%d').date()
        siwes_end_date = datetime.strptime(request.form['siwes_end_date'], '%Y-%m-%d').date()

        if student:
            # Update existing profile
            student.matric_number = matric_number
            student.department = department
            student.institution = institution
            student.siwes_organization_name = siwes_organization_name
            student.siwes_organization_address = siwes_organization_address
            student.siwes_state = siwes_state
            student.siwes_start_date = siwes_start_date
            student.siwes_end_date = siwes_end_date
        else:
            # Create new profile
            student = Student(
                user_id=current_user.id,
                matric_number=matric_number,
                department=department,
                institution=institution,
                siwes_organization_name=siwes_organization_name,
                siwes_organization_address=siwes_organization_address,
                siwes_state=siwes_state,
                siwes_start_date=siwes_start_date,
                siwes_end_date=siwes_end_date
            )
            db.session.add(student)

        # Trigger automatic supervisor assignment for new profiles
        if not student or (student and not student.school_supervisor_id and not student.industry_supervisor_id):
            assignment_results = auto_assign_supervisors(student)

            # Show assignment results to user
            for message in assignment_results['messages']:
                if 'assigned' in message:
                    flash(message, 'success')
                else:
                    flash(message, 'info')

        db.session.commit()
        flash('Profile updated successfully!', 'success')
        return redirect(url_for('student_dashboard'))

    return render_template('student/profile.html', student=student)

# Log Entry Routes
@app.route('/student/logs')
@login_required
def student_logs():
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()
    if not student:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('student_profile'))

    page = request.args.get('page', 1, type=int)
    logs = LogEntry.query.filter_by(student_id=student.id)\
                        .order_by(LogEntry.log_date.desc())\
                        .paginate(page=page, per_page=10, error_out=False)

    return render_template('student/logs.html', logs=logs, student=student)

@app.route('/student/log/<int:log_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_log_entry(log_id):
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()
    if not student:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('student_profile'))

    # Get the log entry and verify ownership
    log_entry = LogEntry.query.get_or_404(log_id)
    if log_entry.student_id != student.id:
        flash('Access denied!', 'error')
        return redirect(url_for('student_logs'))

    # Only allow editing of rejected or pending logs
    if log_entry.status not in ['Rejected', 'Pending']:
        flash('You can only edit rejected or pending log entries.', 'error')
        return redirect(url_for('student_logs'))

    if request.method == 'POST':
        activities = request.form['activities_performed']
        learnings = request.form.get('key_learnings', '')

        if not activities:
            flash('Activities performed is required!', 'error')
            return render_template('student/edit_log.html', log_entry=log_entry)

        # Update the log entry
        log_entry.activities_performed = activities
        log_entry.key_learnings = learnings
        log_entry.status = 'Pending'  # Reset to pending for re-review
        log_entry.industry_review_comments = None  # Clear previous reviews
        log_entry.school_review_comments = None
        log_entry.industry_review_date = None
        log_entry.school_review_date = None
        log_entry.updated_at = datetime.now()

        # Handle file uploads if any
        uploaded_files = request.files.getlist('media_files')
        for file in uploaded_files:
            if file and file.filename and allowed_file(file.filename):
                # Generate unique filename
                unique_filename = generate_unique_filename(file.filename)
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

                # Save file
                file.save(file_path)

                # Create media file record
                media_file = MediaFile(
                    log_entry_id=log_entry.id,
                    filename=unique_filename,
                    original_filename=secure_filename(file.filename),
                    file_type=file.content_type.split('/')[0] if file.content_type else 'unknown',
                    file_size=0,  # Will be updated if needed
                    mime_type=file.content_type or 'application/octet-stream'
                )
                db.session.add(media_file)

        db.session.commit()
        flash('Log entry updated successfully and submitted for re-review!', 'success')
        return redirect(url_for('student_logs'))

    return render_template('student/edit_log.html', log_entry=log_entry)

@app.route('/student/supervisor/school')
@login_required
def view_school_supervisor():
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()
    if not student:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('student_profile'))

    if not student.school_supervisor_id:
        flash('No school supervisor assigned yet.', 'info')
        return redirect(url_for('student_dashboard'))

    # Get school supervisor user and profile
    school_supervisor_user = User.query.get(student.school_supervisor_id)
    school_supervisor_profile = SchoolSupervisor.query.filter_by(user_id=student.school_supervisor_id).first()

    if not school_supervisor_user:
        flash('School supervisor not found.', 'error')
        return redirect(url_for('student_dashboard'))

    return render_template('student/school_supervisor.html',
                         supervisor=school_supervisor_user,
                         supervisor_profile=school_supervisor_profile,
                         student=student)

@app.route('/student/logs/new', methods=['GET', 'POST'])
@login_required
def new_log_entry():
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()
    if not student:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('student_profile'))

    if request.method == 'POST':
        log_date = datetime.strptime(request.form['log_date'], '%Y-%m-%d').date()
        activities_performed = request.form['activities_performed']
        key_learnings = request.form['key_learnings']

        # Check if log entry already exists for this date
        existing_log = LogEntry.query.filter_by(student_id=student.id, log_date=log_date).first()
        if existing_log:
            flash('A log entry already exists for this date!', 'error')
            return redirect(url_for('new_log_entry'))

        # Create new log entry
        log_entry = LogEntry(
            student_id=student.id,
            log_date=log_date,
            activities_performed=activities_performed,
            key_learnings=key_learnings,
            status='Pending'
        )

        db.session.add(log_entry)
        db.session.flush()  # Get the log entry ID

        # Handle file uploads
        uploaded_files = request.files.getlist('media_files')
        for file in uploaded_files:
            if file and file.filename and allowed_file(file.filename):
                # Generate unique filename
                unique_filename = generate_unique_filename(file.filename)
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

                # Save file
                file.save(file_path)

                # Create media file record
                media_file = MediaFile(
                    log_entry_id=log_entry.id,
                    filename=unique_filename,
                    original_filename=secure_filename(file.filename),
                    file_type=get_file_type(file.filename),
                    file_size=os.path.getsize(file_path),
                    mime_type=file.content_type or 'application/octet-stream'
                )

                db.session.add(media_file)

        db.session.commit()

        flash('Log entry created successfully!', 'success')
        return redirect(url_for('student_logs'))

    return render_template('student/new_log.html', student=student)

@app.route('/student/logs/<int:log_id>')
@login_required
def view_log_entry(log_id):
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()
    if not student:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('student_profile'))

    log_entry = LogEntry.query.filter_by(id=log_id, student_id=student.id).first()
    if not log_entry:
        flash('Log entry not found!', 'error')
        return redirect(url_for('student_logs'))

    return render_template('student/view_log.html', log=log_entry, student=student)



# File serving routes
@app.route('/uploads/<filename>')
@login_required
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/uploads/profiles/<filename>')
def uploaded_profile(filename):
    return send_from_directory(app.config['PROFILE_UPLOAD_FOLDER'], filename)

@app.route('/uploads/signatures/<filename>')
@login_required
def uploaded_signature(filename):
    return send_from_directory(app.config['SIGNATURE_UPLOAD_FOLDER'], filename)

@app.route('/profile/picture', methods=['GET', 'POST'])
@login_required
def profile_picture():
    if request.method == 'POST':
        if 'profile_picture' not in request.files:
            flash('No file selected!', 'error')
            return redirect(request.url)

        file = request.files['profile_picture']
        if file.filename == '':
            flash('No file selected!', 'error')
            return redirect(request.url)

        if file and allowed_profile_file(file.filename):
            # Delete old profile picture if exists
            if current_user.profile_picture:
                delete_profile_picture(current_user.profile_picture)

            # Save new profile picture
            filename = save_profile_picture(file, current_user.id)
            if filename:
                current_user.profile_picture = filename
                db.session.commit()
                flash('Profile picture updated successfully!', 'success')
            else:
                flash('Error uploading profile picture!', 'error')
        else:
            flash('Invalid file type! Please upload an image file (PNG, JPG, JPEG, GIF, BMP, WEBP).', 'error')

    return render_template('profile_picture.html')

@app.route('/profile/picture/delete', methods=['POST'])
@login_required
def delete_profile_picture_route():
    if current_user.profile_picture:
        delete_profile_picture(current_user.profile_picture)
        current_user.profile_picture = None
        db.session.commit()
        flash('Profile picture deleted successfully!', 'success')
    else:
        flash('No profile picture to delete!', 'info')

    return redirect(url_for('profile_picture'))

@app.route('/student/logs/<int:log_id>/delete-media/<int:media_id>', methods=['POST'])
@login_required
def delete_media_file(log_id, media_id):
    if current_user.role.name != 'Student':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.filter_by(user_id=current_user.id).first()
    if not student:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('student_profile'))

    log_entry = LogEntry.query.filter_by(id=log_id, student_id=student.id).first()
    if not log_entry:
        flash('Log entry not found!', 'error')
        return redirect(url_for('student_logs'))

    # Only allow deletion if status is Pending
    if log_entry.status != 'Pending':
        flash('Cannot delete media from reviewed log entries!', 'error')
        return redirect(url_for('view_log_entry', log_id=log_id))

    media_file = MediaFile.query.filter_by(id=media_id, log_entry_id=log_id).first()
    if not media_file:
        flash('Media file not found!', 'error')
        return redirect(url_for('view_log_entry', log_id=log_id))

    # Delete physical file
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], media_file.filename)
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete database record
    db.session.delete(media_file)
    db.session.commit()

    flash('Media file deleted successfully!', 'success')
    return redirect(url_for('view_log_entry', log_id=log_id))

# Supervisor Routes
@app.route('/supervisor/students')
@login_required
def supervisor_students():
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get students assigned to this supervisor
    if current_user.role.name == 'Industry Supervisor':
        students = Student.query.filter_by(industry_supervisor_id=current_user.id).all()
    else:
        students = Student.query.filter_by(school_supervisor_id=current_user.id).all()

    return render_template('supervisor/students.html', students=students, supervisor_type=current_user.role.name)

@app.route('/supervisor/student/<int:student_id>/monthly-comment', methods=['GET', 'POST'])
@login_required
def add_monthly_comment(student_id):
    if current_user.role.name != 'School Supervisor':
        flash('Access denied! Only school supervisors can add monthly comments.', 'error')
        return redirect(url_for('index'))

    # Get school supervisor profile
    school_profile = SchoolSupervisor.query.filter_by(user_id=current_user.id).first()
    if not school_profile:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('supervisor_profile'))

    # Get student and verify assignment
    student = Student.query.get_or_404(student_id)
    if student.school_supervisor_id != current_user.id:
        flash('You can only add comments for your assigned students.', 'error')
        return redirect(url_for('supervisor_students'))

    # Get current month and year
    current_date = datetime.now()
    current_month = current_date.month
    current_year = current_date.year

    # Check if comment already exists for this month
    existing_comment = MonthlyComment.query.filter_by(
        student_id=student.id,
        school_supervisor_id=current_user.id,
        month=current_month,
        year=current_year
    ).first()

    if request.method == 'POST':
        comment_text = request.form['comment']
        performance_rating = request.form['performance_rating']

        if not comment_text:
            flash('Comment is required!', 'error')
            return render_template('supervisor/monthly_comment.html',
                                 student=student,
                                 existing_comment=existing_comment,
                                 current_month=current_month,
                                 current_year=current_year)

        if existing_comment:
            # Update existing comment
            existing_comment.comment = comment_text
            existing_comment.performance_rating = performance_rating
            existing_comment.created_at = datetime.now()
            flash('Monthly comment updated successfully!', 'success')
        else:
            # Create new comment
            monthly_comment = MonthlyComment(
                student_id=student.id,
                school_supervisor_id=current_user.id,
                month=current_month,
                year=current_year,
                comment=comment_text,
                performance_rating=performance_rating
            )
            db.session.add(monthly_comment)
            flash('Monthly comment added successfully!', 'success')

        db.session.commit()
        return redirect(url_for('supervisor_students'))

    # Get student's logs for the current month
    from sqlalchemy import extract
    month_logs = LogEntry.query.filter(
        LogEntry.student_id == student.id,
        extract('month', LogEntry.log_date) == current_month,
        extract('year', LogEntry.log_date) == current_year
    ).order_by(LogEntry.log_date.asc()).all()

    return render_template('supervisor/monthly_comment.html',
                         student=student,
                         existing_comment=existing_comment,
                         month_logs=month_logs,
                         current_month=current_month,
                         current_year=current_year)

@app.route('/supervisor/reports')
@login_required
def supervisor_reports():
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get students assigned to this supervisor
    if current_user.role.name == 'Industry Supervisor':
        students = Student.query.filter_by(industry_supervisor_id=current_user.id).all()
    else:
        students = Student.query.filter_by(school_supervisor_id=current_user.id).all()

    # Calculate statistics
    total_students = len(students)

    # Get all log entries for assigned students
    all_logs = []
    for student in students:
        logs = LogEntry.query.filter_by(student_id=student.id).all()
        all_logs.extend(logs)

    total_logs = len(all_logs)
    approved_logs = len([log for log in all_logs if log.status == 'Approved'])
    pending_logs = len([log for log in all_logs if log.status == 'Pending'])
    rejected_logs = len([log for log in all_logs if log.status == 'Rejected'])

    # Department/Company statistics
    from sqlalchemy import func
    if current_user.role.name == 'School Supervisor':
        dept_stats = db.session.query(Student.department, func.count(Student.id))\
                              .filter(Student.school_supervisor_id == current_user.id)\
                              .group_by(Student.department)\
                              .all()
    else:
        # For industry supervisors, group by organization
        dept_stats = db.session.query(Student.siwes_organization_name, func.count(Student.id))\
                              .filter(Student.industry_supervisor_id == current_user.id)\
                              .group_by(Student.siwes_organization_name)\
                              .all()

    # Recent activity
    recent_logs = LogEntry.query.join(Student)\
                                .filter(Student.industry_supervisor_id == current_user.id if current_user.role.name == 'Industry Supervisor' else Student.school_supervisor_id == current_user.id)\
                                .order_by(LogEntry.created_at.desc())\
                                .limit(10).all()

    return render_template('supervisor/reports.html',
                         supervisor_type=current_user.role.name,
                         total_students=total_students,
                         total_logs=total_logs,
                         approved_logs=approved_logs,
                         pending_logs=pending_logs,
                         rejected_logs=rejected_logs,
                         dept_stats=dept_stats,
                         recent_logs=recent_logs,
                         students=students)

@app.route('/supervisor/reports/export/<report_type>')
@login_required
def supervisor_export_report(report_type):
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    if report_type == 'students_csv':
        return supervisor_export_students_csv()
    elif report_type == 'logs_csv':
        return supervisor_export_logs_csv()
    elif report_type == 'summary_csv':
        return supervisor_export_summary_csv()
    else:
        flash('Invalid report type!', 'error')
        return redirect(url_for('supervisor_reports'))

@app.route('/supervisor/reviews')
@login_required
def supervisor_reviews():
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get students assigned to this supervisor
    if current_user.role.name == 'Industry Supervisor':
        students = Student.query.filter_by(industry_supervisor_id=current_user.id).all()
    else:
        students = Student.query.filter_by(school_supervisor_id=current_user.id).all()

    # Get pending log entries for review
    pending_logs = []
    for student in students:
        logs = LogEntry.query.filter_by(student_id=student.id, status='Pending').all()
        pending_logs.extend(logs)

    # Sort by date
    pending_logs.sort(key=lambda x: x.log_date, reverse=True)

    return render_template('supervisor/reviews.html', pending_logs=pending_logs, supervisor_type=current_user.role.name)

@app.route('/supervisor/review/<int:log_id>', methods=['GET', 'POST'])
@login_required
def review_log_entry(log_id):
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    log_entry = LogEntry.query.get_or_404(log_id)

    # Check if supervisor is assigned to this student
    if current_user.role.name == 'Industry Supervisor':
        if log_entry.student.industry_supervisor_id != current_user.id:
            flash('Access denied!', 'error')
            return redirect(url_for('supervisor_reviews'))
    else:
        if log_entry.student.school_supervisor_id != current_user.id:
            flash('Access denied!', 'error')
            return redirect(url_for('supervisor_reviews'))

    if request.method == 'POST':
        action = request.form['action']
        comments = request.form['comments']

        if current_user.role.name == 'Industry Supervisor':
            log_entry.industry_review_comments = comments
            log_entry.industry_review_date = datetime.utcnow()
        else:
            log_entry.school_review_comments = comments
            log_entry.school_review_date = datetime.utcnow()

        if action == 'approve':
            log_entry.status = 'Approved'
            flash('Log entry approved successfully!', 'success')
        elif action == 'reject':
            log_entry.status = 'Rejected'
            flash('Log entry rejected. Student has been notified.', 'info')

        db.session.commit()
        return redirect(url_for('supervisor_reviews'))

    return render_template('supervisor/review_log.html', log=log_entry, supervisor_type=current_user.role.name)

@app.route('/supervisor/student/<int:student_id>')
@login_required
def view_student_details(student_id):
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.get_or_404(student_id)

    # Check if supervisor is assigned to this student
    if current_user.role.name == 'Industry Supervisor':
        if student.industry_supervisor_id != current_user.id:
            flash('Access denied!', 'error')
            return redirect(url_for('supervisor_students'))
    else:
        if student.school_supervisor_id != current_user.id:
            flash('Access denied!', 'error')
            return redirect(url_for('supervisor_students'))

    # Get student's log entries
    logs = LogEntry.query.filter_by(student_id=student.id).order_by(LogEntry.log_date.desc()).all()

    # Calculate statistics
    total_logs = len(logs)
    approved_logs = len([log for log in logs if log.status == 'Approved'])
    pending_logs = len([log for log in logs if log.status == 'Pending'])
    rejected_logs = len([log for log in logs if log.status == 'Rejected'])

    return render_template('supervisor/student_details.html',
                         student=student,
                         logs=logs,
                         total_logs=total_logs,
                         approved_logs=approved_logs,
                         pending_logs=pending_logs,
                         rejected_logs=rejected_logs,
                         supervisor_type=current_user.role.name)

@app.route('/supervisor/profile', methods=['GET', 'POST'])
@login_required
def supervisor_profile():
    if current_user.role.name not in ['Industry Supervisor', 'School Supervisor']:
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    if current_user.role.name == 'School Supervisor':
        profile = SchoolSupervisor.query.filter_by(user_id=current_user.id).first()

        if request.method == 'POST':
            institution = request.form['institution']
            department = request.form['department']
            faculty = request.form.get('faculty', '')
            position = request.form.get('position', '')
            office_location = request.form.get('office_location', '')
            specialization = request.form.get('specialization', '')
            years_of_experience = request.form.get('years_of_experience', type=int)

            # Handle signature upload
            signature_file = request.files.get('signature_file')
            signature_filename = None
            if signature_file and signature_file.filename:
                signature_filename = save_signature_file(signature_file, current_user.id, 'school')
                if not signature_filename:
                    flash('Invalid signature file format. Please upload PNG, JPG, JPEG, GIF, BMP, or WEBP files only.', 'error')
                    return redirect(url_for('supervisor_profile'))

            if profile:
                # Update existing profile
                profile.institution = institution
                profile.department = department
                profile.faculty = faculty
                profile.position = position
                profile.office_location = office_location
                profile.specialization = specialization
                profile.years_of_experience = years_of_experience
                if signature_filename:
                    profile.signature_filename = signature_filename
            else:
                # Create new profile
                profile = SchoolSupervisor(
                    user_id=current_user.id,
                    institution=institution,
                    department=department,
                    faculty=faculty,
                    position=position,
                    office_location=office_location,
                    specialization=specialization,
                    years_of_experience=years_of_experience,
                    signature_filename=signature_filename
                )
                db.session.add(profile)

            db.session.commit()
            flash('Profile updated successfully!', 'success')
            return redirect(url_for('supervisor_dashboard'))

        return render_template('supervisor/school_profile.html', profile=profile)

    else:  # Industry Supervisor
        profile = IndustrySupervisor.query.filter_by(user_id=current_user.id).first()

        if request.method == 'POST':
            company_name = request.form['company_name']
            industry_type = request.form['industry_type']
            position = request.form['position']
            department = request.form.get('department', '')
            company_address = request.form.get('company_address', '')
            company_size = request.form.get('company_size', '')
            years_of_experience = request.form.get('years_of_experience', type=int)
            specialization = request.form.get('specialization', '')

            # Handle signature upload
            signature_file = request.files.get('signature_file')
            signature_filename = None
            if signature_file and signature_file.filename:
                signature_filename = save_signature_file(signature_file, current_user.id, 'industry')
                if not signature_filename:
                    flash('Invalid signature file format. Please upload PNG, JPG, JPEG, GIF, BMP, or WEBP files only.', 'error')
                    return redirect(url_for('supervisor_profile'))

            if profile:
                # Update existing profile
                profile.company_name = company_name
                profile.industry_type = industry_type
                profile.position = position
                profile.department = department
                profile.company_address = company_address
                profile.company_size = company_size
                profile.years_of_experience = years_of_experience
                profile.specialization = specialization
                if signature_filename:
                    profile.signature_filename = signature_filename
            else:
                # Create new profile
                profile = IndustrySupervisor(
                    user_id=current_user.id,
                    company_name=company_name,
                    industry_type=industry_type,
                    position=position,
                    department=department,
                    company_address=company_address,
                    company_size=company_size,
                    years_of_experience=years_of_experience,
                    specialization=specialization,
                    signature_filename=signature_filename
                )
                db.session.add(profile)

            db.session.commit()
            flash('Profile updated successfully!', 'success')
            return redirect(url_for('supervisor_dashboard'))

        return render_template('supervisor/industry_profile.html', profile=profile)

@app.route('/itf/profile', methods=['GET', 'POST'])
@login_required
def itf_profile():
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    profile = ITFSupervisor.query.filter_by(user_id=current_user.id).first()

    if request.method == 'POST':
        assigned_state = request.form['assigned_state']
        office_location = request.form.get('office_location', '')
        position = request.form.get('position', '')
        years_of_experience = request.form.get('years_of_experience', type=int)
        specialization = request.form.get('specialization', '')

        # Handle signature upload
        signature_file = request.files.get('signature_file')
        signature_filename = None
        if signature_file and signature_file.filename:
            signature_filename = save_signature_file(signature_file, current_user.id, 'itf')
            if not signature_filename:
                flash('Invalid signature file format. Please upload PNG, JPG, JPEG, GIF, BMP, or WEBP files only.', 'error')
                return redirect(url_for('itf_profile'))

        if profile:
            # Update existing profile
            profile.assigned_state = assigned_state
            profile.office_location = office_location
            profile.position = position
            profile.years_of_experience = years_of_experience
            profile.specialization = specialization
            if signature_filename:
                profile.signature_filename = signature_filename
        else:
            # Create new profile
            profile = ITFSupervisor(
                user_id=current_user.id,
                assigned_state=assigned_state,
                office_location=office_location,
                position=position,
                years_of_experience=years_of_experience,
                specialization=specialization,
                signature_filename=signature_filename
            )
            db.session.add(profile)

        db.session.commit()
        flash('Profile updated successfully!', 'success')
        return redirect(url_for('itf_dashboard'))

    return render_template('itf/profile.html', profile=profile)

# Signature Management Routes
@app.route('/supervisor/sign-weekly/<int:student_id>', methods=['POST'])
@login_required
def sign_weekly(student_id):
    if current_user.role.name != 'Industry Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.get_or_404(student_id)

    # Check if this supervisor is assigned to this student
    if student.industry_supervisor_id != current_user.id:
        flash('You are not authorized to sign for this student.', 'error')
        return redirect(url_for('supervisor_dashboard'))

    # Get the current week's start and end dates
    today = date.today()
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)

    # Check if already signed for this week
    existing_signature = WeeklySignature.query.filter_by(
        student_id=student_id,
        industry_supervisor_id=current_user.id,
        week_start_date=week_start
    ).first()

    if existing_signature:
        flash('You have already signed for this week.', 'warning')
        return redirect(url_for('supervisor_view_student', student_id=student_id))

    comments = request.form.get('comments', '')

    # Create weekly signature record
    weekly_signature = WeeklySignature(
        student_id=student_id,
        industry_supervisor_id=current_user.id,
        week_start_date=week_start,
        week_end_date=week_end,
        comments=comments
    )

    db.session.add(weekly_signature)
    db.session.commit()

    flash('Weekly signature added successfully!', 'success')
    return redirect(url_for('view_student_details', student_id=student_id))

@app.route('/supervisor/sign-monthly/<int:student_id>', methods=['POST'])
@login_required
def sign_monthly(student_id):
    if current_user.role.name != 'School Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.get_or_404(student_id)

    # Check if this supervisor is assigned to this student
    if student.school_supervisor_id != current_user.id:
        flash('You are not authorized to sign for this student.', 'error')
        return redirect(url_for('supervisor_dashboard'))

    # Get current month and year
    today = date.today()
    current_month = today.month
    current_year = today.year

    # Check if already signed for this month
    existing_signature = MonthlySignature.query.filter_by(
        student_id=student_id,
        school_supervisor_id=current_user.id,
        month=current_month,
        year=current_year
    ).first()

    if existing_signature:
        flash('You have already signed for this month.', 'warning')
        return redirect(url_for('supervisor_view_student', student_id=student_id))

    comments = request.form.get('comments', '')

    # Create monthly signature record
    monthly_signature = MonthlySignature(
        student_id=student_id,
        school_supervisor_id=current_user.id,
        month=current_month,
        year=current_year,
        comments=comments
    )

    db.session.add(monthly_signature)
    db.session.commit()

    flash('Monthly signature added successfully!', 'success')
    return redirect(url_for('view_student_details', student_id=student_id))

@app.route('/itf/sign-final/<int:student_id>', methods=['POST'])
@login_required
def sign_final(student_id):
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.get_or_404(student_id)

    # Get ITF supervisor profile to check assigned state
    itf_profile = ITFSupervisor.query.filter_by(user_id=current_user.id).first()

    if not itf_profile or student.siwes_state != itf_profile.assigned_state:
        flash('You are not authorized to sign for this student.', 'error')
        return redirect(url_for('itf_dashboard'))

    # Check if already signed
    existing_signature = FinalSignature.query.filter_by(
        student_id=student_id,
        itf_supervisor_id=current_user.id
    ).first()

    if existing_signature:
        flash('You have already provided final signature for this student.', 'warning')
        return redirect(url_for('itf_view_student', student_id=student_id))

    # Calculate logbook completion percentage
    total_days = 0
    logged_days = 0

    if student.siwes_start_date and student.siwes_end_date:
        total_days = (student.siwes_end_date - student.siwes_start_date).days + 1
        logged_days = LogEntry.query.filter_by(student_id=student_id).count()

    completion_percentage = (logged_days / total_days * 100) if total_days > 0 else 0

    # Check if completion is at least 60%
    if completion_percentage < 60:
        flash(f'Student has only completed {completion_percentage:.1f}% of logbook. Minimum 60% required for final signature.', 'error')
        return redirect(url_for('itf_view_student', student_id=student_id))

    comments = request.form.get('comments', '')

    # Create final signature record
    final_signature = FinalSignature(
        student_id=student_id,
        itf_supervisor_id=current_user.id,
        comments=comments,
        logbook_completion_percentage=completion_percentage
    )

    db.session.add(final_signature)
    db.session.commit()

    flash(f'Final signature added successfully! Logbook completion: {completion_percentage:.1f}%', 'success')
    return redirect(url_for('itf_view_student', student_id=student_id))

# ITF Routes
@app.route('/itf/students')
@login_required
def itf_students():
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get ITF supervisor profile to check assigned state
    itf_profile = ITFSupervisor.query.filter_by(user_id=current_user.id).first()

    if not itf_profile:
        flash('Please complete your profile first to specify your assigned state.', 'warning')
        return redirect(url_for('itf_profile'))

    page = request.args.get('page', 1, type=int)

    # Filter students by the ITF supervisor's assigned state
    students = Student.query.filter_by(siwes_state=itf_profile.assigned_state)\
                           .order_by(Student.created_at.desc())\
                           .paginate(page=page, per_page=20, error_out=False)

    return render_template('itf/students.html',
                         students=students,
                         assigned_state=itf_profile.assigned_state)

@app.route('/itf/student/<int:student_id>')
@login_required
def itf_view_student(student_id):
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get ITF supervisor profile to check assigned state
    itf_profile = ITFSupervisor.query.filter_by(user_id=current_user.id).first()

    if not itf_profile:
        flash('Please complete your profile first to specify your assigned state.', 'warning')
        return redirect(url_for('itf_profile'))

    # Get student and verify they are in the ITF supervisor's assigned state
    student = Student.query.get_or_404(student_id)

    if student.siwes_state != itf_profile.assigned_state:
        flash('You can only view students in your assigned state.', 'error')
        return redirect(url_for('itf_students'))

    # Get student's log entries with pagination
    page = request.args.get('page', 1, type=int)
    logs = LogEntry.query.filter_by(student_id=student.id)\
                        .order_by(LogEntry.log_date.desc())\
                        .paginate(page=page, per_page=10, error_out=False)

    # Get statistics for this student
    total_logs = LogEntry.query.filter_by(student_id=student.id).count()
    approved_logs = LogEntry.query.filter_by(student_id=student.id, status='Approved').count()
    pending_logs = LogEntry.query.filter_by(student_id=student.id, status='Pending').count()
    rejected_logs = LogEntry.query.filter_by(student_id=student.id, status='Rejected').count()

    return render_template('itf/student_profile.html',
                         student=student,
                         logs=logs,
                         total_logs=total_logs,
                         approved_logs=approved_logs,
                         pending_logs=pending_logs,
                         rejected_logs=rejected_logs,
                         assigned_state=itf_profile.assigned_state)

@app.route('/itf/student/<int:student_id>/export-logbook')
@login_required
def itf_export_student_logbook(student_id):
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get ITF supervisor profile to check assigned state
    itf_profile = ITFSupervisor.query.filter_by(user_id=current_user.id).first()

    if not itf_profile:
        flash('Please complete your profile first to specify your assigned state.', 'warning')
        return redirect(url_for('itf_profile'))

    # Get student and verify they are in the ITF supervisor's assigned state
    student = Student.query.get_or_404(student_id)

    if student.siwes_state != itf_profile.assigned_state:
        flash('You can only export logbooks for students in your assigned state.', 'error')
        return redirect(url_for('itf_students'))

    # Get all log entries for the student, ordered by date
    logs = LogEntry.query.filter_by(student_id=student.id)\
                        .order_by(LogEntry.log_date.asc())\
                        .all()

    # Generate HTML logbook for now (can be printed to PDF)
    return render_template('itf/student_logbook.html',
                         student=student,
                         logs=logs,
                         export_date=datetime.now())

@app.route('/itf/reports')
@login_required
def itf_reports():
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Generate comprehensive reports
    total_students = Student.query.count()
    total_logs = LogEntry.query.count()
    pending_logs = LogEntry.query.filter_by(status='Pending').count()
    approved_logs = LogEntry.query.filter_by(status='Approved').count()
    rejected_logs = LogEntry.query.filter_by(status='Rejected').count()

    # Students by department
    from sqlalchemy import func
    dept_stats = db.session.query(Student.department, func.count(Student.id))\
                          .group_by(Student.department)\
                          .all()

    # Recent activity
    recent_logs = LogEntry.query.order_by(LogEntry.created_at.desc()).limit(10).all()

    return render_template('itf/reports.html',
                         total_students=total_students,
                         total_logs=total_logs,
                         pending_logs=pending_logs,
                         approved_logs=approved_logs,
                         rejected_logs=rejected_logs,
                         dept_stats=dept_stats,
                         recent_logs=recent_logs)

@app.route('/itf/reports/export/<report_type>')
@login_required
def itf_export_report(report_type):
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get ITF supervisor profile to check assigned state
    itf_profile = ITFSupervisor.query.filter_by(user_id=current_user.id).first()

    if not itf_profile:
        flash('Please complete your profile first.', 'warning')
        return redirect(url_for('itf_profile'))

    if report_type == 'students_csv':
        return itf_export_students_csv(itf_profile.assigned_state)
    elif report_type == 'logs_csv':
        return itf_export_logs_csv(itf_profile.assigned_state)
    elif report_type == 'summary_csv':
        return itf_export_summary_csv(itf_profile.assigned_state)
    else:
        flash('Invalid report type!', 'error')
        return redirect(url_for('itf_reports'))

@app.route('/itf/analytics')
@login_required
def itf_analytics():
    if current_user.role.name != 'ITF Supervisor':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get ITF supervisor profile to check assigned state
    itf_profile = ITFSupervisor.query.filter_by(user_id=current_user.id).first()

    if not itf_profile:
        flash('Please complete your profile first to specify your assigned state.', 'warning')
        return redirect(url_for('itf_profile'))

    # Generate comprehensive analytics data
    from sqlalchemy import func, extract

    # Basic statistics for ITF supervisor's assigned state
    state_students = Student.query.filter_by(siwes_state=itf_profile.assigned_state)
    total_state_students = state_students.count()

    # Get all students for comparison
    total_all_students = Student.query.count()
    total_all_logs = LogEntry.query.count()

    # Log statistics for state students
    state_student_ids = [s.id for s in state_students.all()]
    state_logs = LogEntry.query.filter(LogEntry.student_id.in_(state_student_ids))
    total_state_logs = state_logs.count()
    approved_state_logs = state_logs.filter_by(status='Approved').count()
    pending_state_logs = state_logs.filter_by(status='Pending').count()
    rejected_state_logs = state_logs.filter_by(status='Rejected').count()

    # Department distribution for state students
    dept_stats = db.session.query(Student.department, func.count(Student.id))\
                          .filter(Student.siwes_state == itf_profile.assigned_state)\
                          .group_by(Student.department)\
                          .all()

    # Institution distribution for state students
    inst_stats = db.session.query(Student.institution, func.count(Student.id))\
                          .filter(Student.siwes_state == itf_profile.assigned_state)\
                          .group_by(Student.institution)\
                          .all()

    # Supervisor assignment statistics for state students
    state_students_with_industry = state_students.filter(Student.industry_supervisor_id.isnot(None)).count()
    state_students_with_school = state_students.filter(Student.school_supervisor_id.isnot(None)).count()
    state_fully_assigned = state_students.filter(
        Student.industry_supervisor_id.isnot(None),
        Student.school_supervisor_id.isnot(None)
    ).count()

    # Monthly registration trends for state students (last 6 months)
    monthly_registrations = db.session.query(
        extract('year', Student.created_at).label('year'),
        extract('month', Student.created_at).label('month'),
        func.count(Student.id).label('count')
    ).filter(Student.siwes_state == itf_profile.assigned_state)\
     .group_by(
        extract('year', Student.created_at),
        extract('month', Student.created_at)
    ).order_by(
        extract('year', Student.created_at).desc(),
        extract('month', Student.created_at).desc()
    ).limit(6).all()

    # Monthly log submission trends for state students (last 6 months)
    monthly_logs = db.session.query(
        extract('year', LogEntry.created_at).label('year'),
        extract('month', LogEntry.created_at).label('month'),
        func.count(LogEntry.id).label('count')
    ).join(Student, LogEntry.student_id == Student.id)\
     .filter(Student.siwes_state == itf_profile.assigned_state)\
     .group_by(
        extract('year', LogEntry.created_at),
        extract('month', LogEntry.created_at)
    ).order_by(
        extract('year', LogEntry.created_at).desc(),
        extract('month', LogEntry.created_at).desc()
    ).limit(6).all()

    # Top performing students (highest approval rate with minimum 5 logs)
    # Use a simpler approach to avoid SQLAlchemy case statement issues
    state_students_with_logs = db.session.query(Student)\
                                        .join(LogEntry, Student.id == LogEntry.student_id)\
                                        .filter(Student.siwes_state == itf_profile.assigned_state)\
                                        .group_by(Student.id)\
                                        .having(func.count(LogEntry.id) >= 5)\
                                        .all()

    # Calculate approval rates manually
    top_students = []
    for student in state_students_with_logs:
        total_logs = LogEntry.query.filter_by(student_id=student.id).count()
        approved_logs = LogEntry.query.filter_by(student_id=student.id, status='Approved').count()
        if total_logs >= 5:
            top_students.append((student, total_logs, approved_logs))

    # Sort by approval rate and take top 5
    top_students.sort(key=lambda x: x[2] / x[1] if x[1] > 0 else 0, reverse=True)
    top_students = top_students[:5]

    # Calculate review response times
    reviewed_logs = LogEntry.query.join(Student)\
                                  .filter(Student.siwes_state == itf_profile.assigned_state)\
                                  .filter(LogEntry.status.in_(['Approved', 'Rejected']))\
                                  .all()

    review_times = []
    for log in reviewed_logs:
        if log.industry_review_date:
            days_diff = (log.industry_review_date.date() - log.log_date).days
            review_times.append(days_diff)
        if log.school_review_date:
            days_diff = (log.school_review_date.date() - log.log_date).days
            review_times.append(days_diff)

    avg_review_time = sum(review_times) / len(review_times) if review_times else 0

    # Calculate performance metrics
    participation_rate = (total_state_students / total_all_students * 100) if total_all_students > 0 else 0
    approval_rate = (approved_state_logs / total_state_logs * 100) if total_state_logs > 0 else 0
    assignment_rate = (state_fully_assigned / total_state_students * 100) if total_state_students > 0 else 0

    # Recent activity
    recent_students = state_students.order_by(Student.created_at.desc()).limit(5).all()
    recent_logs = LogEntry.query.join(Student)\
                                .filter(Student.siwes_state == itf_profile.assigned_state)\
                                .order_by(LogEntry.created_at.desc())\
                                .limit(10).all()

    return render_template('itf/analytics.html',
                         assigned_state=itf_profile.assigned_state,
                         total_state_students=total_state_students,
                         total_state_logs=total_state_logs,
                         approved_state_logs=approved_state_logs,
                         pending_state_logs=pending_state_logs,
                         rejected_state_logs=rejected_state_logs,
                         dept_stats=dept_stats,
                         inst_stats=inst_stats,
                         state_students_with_industry=state_students_with_industry,
                         state_students_with_school=state_students_with_school,
                         state_fully_assigned=state_fully_assigned,
                         monthly_registrations=monthly_registrations,
                         monthly_logs=monthly_logs,
                         top_students=top_students,
                         avg_review_time=avg_review_time,
                         participation_rate=participation_rate,
                         approval_rate=approval_rate,
                         assignment_rate=assignment_rate,
                         recent_students=recent_students,
                         recent_logs=recent_logs)

# Admin Routes
@app.route('/admin/users')
@login_required
def admin_users():
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc())\
                     .paginate(page=page, per_page=20, error_out=False)

    return render_template('admin/users.html', users=users)

@app.route('/admin/system')
@login_required
def admin_system():
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # System statistics
    total_users = User.query.count()
    total_students = Student.query.count()
    total_logs = LogEntry.query.count()

    # Recent activity
    recent_users = User.query.order_by(User.created_at.desc()).limit(10).all()
    recent_students = Student.query.order_by(Student.created_at.desc()).limit(10).all()

    return render_template('admin/system.html',
                         total_users=total_users,
                         total_students=total_students,
                         total_logs=total_logs,
                         recent_users=recent_users,
                         recent_students=recent_students)

@app.route('/admin/assign-supervisor', methods=['GET', 'POST'])
@login_required
def assign_supervisor():
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        student_id = request.form['student_id']
        industry_supervisor_id = request.form.get('industry_supervisor_id')
        school_supervisor_id = request.form.get('school_supervisor_id')

        student = Student.query.get_or_404(student_id)

        if industry_supervisor_id:
            student.industry_supervisor_id = industry_supervisor_id
        if school_supervisor_id:
            student.school_supervisor_id = school_supervisor_id

        db.session.commit()
        flash('Supervisor assigned successfully!', 'success')
        return redirect(url_for('assign_supervisor'))

    # Get students with their details
    students = Student.query.all()

    # Get supervisors with their profiles
    industry_supervisors = db.session.query(User, IndustrySupervisor)\
                                   .join(IndustrySupervisor, User.id == IndustrySupervisor.user_id)\
                                   .filter(User.role_id == 2).all()  # Industry Supervisor role

    school_supervisors = db.session.query(User, SchoolSupervisor)\
                                 .join(SchoolSupervisor, User.id == SchoolSupervisor.user_id)\
                                 .filter(User.role_id == 3).all()   # School Supervisor role

    # Get supervisors without profiles (for fallback)
    industry_users_without_profile = User.query.filter(
        User.role_id == 2,
        ~User.id.in_(db.session.query(IndustrySupervisor.user_id))
    ).all()

    school_users_without_profile = User.query.filter(
        User.role_id == 3,
        ~User.id.in_(db.session.query(SchoolSupervisor.user_id))
    ).all()

    return render_template('admin/assign_supervisor.html',
                         students=students,
                         industry_supervisors=industry_supervisors,
                         school_supervisors=school_supervisors,
                         industry_users_without_profile=industry_users_without_profile,
                         school_users_without_profile=school_users_without_profile)

@app.route('/admin/get-matching-supervisors/<int:student_id>')
@login_required
def get_matching_supervisors(student_id):
    if current_user.role.name != 'Admin':
        return jsonify({'error': 'Access denied'}), 403

    student = Student.query.get_or_404(student_id)

    # Find matching school supervisors (same institution and department)
    matching_school_supervisors = db.session.query(User, SchoolSupervisor)\
        .join(SchoolSupervisor, User.id == SchoolSupervisor.user_id)\
        .filter(
            SchoolSupervisor.institution == student.institution,
            SchoolSupervisor.department == student.department
        ).all()

    # Find matching industry supervisors (same industry type as student's organization)
    # This is a simplified match - in practice, you might want more sophisticated matching
    matching_industry_supervisors = db.session.query(User, IndustrySupervisor)\
        .join(IndustrySupervisor, User.id == IndustrySupervisor.user_id)\
        .all()  # For now, show all industry supervisors

    return jsonify({
        'school_supervisors': [
            {
                'id': user.id,
                'name': user.name,
                'institution': profile.institution,
                'department': profile.department,
                'position': profile.position
            }
            for user, profile in matching_school_supervisors
        ],
        'industry_supervisors': [
            {
                'id': user.id,
                'name': user.name,
                'company': profile.company_name,
                'industry': profile.industry_type,
                'position': profile.position
            }
            for user, profile in matching_industry_supervisors
        ]
    })

@app.route('/admin/auto-assign-all', methods=['POST'])
@login_required
def auto_assign_all_students():
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get all students without supervisors
    unassigned_students = Student.query.filter(
        (Student.school_supervisor_id.is_(None)) |
        (Student.industry_supervisor_id.is_(None))
    ).all()

    assignment_summary = {
        'total_processed': 0,
        'school_assigned': 0,
        'industry_assigned': 0,
        'failed_assignments': []
    }

    for student in unassigned_students:
        assignment_results = auto_assign_supervisors(student)
        assignment_summary['total_processed'] += 1

        if assignment_results['school_assigned']:
            assignment_summary['school_assigned'] += 1

        if assignment_results['industry_assigned']:
            assignment_summary['industry_assigned'] += 1

        if not assignment_results['school_assigned'] and not assignment_results['industry_assigned']:
            assignment_summary['failed_assignments'].append(student.user.name)

    db.session.commit()

    # Flash summary messages
    flash(f"Processed {assignment_summary['total_processed']} students", 'info')
    flash(f"School supervisors assigned: {assignment_summary['school_assigned']}", 'success')
    flash(f"Industry supervisors assigned: {assignment_summary['industry_assigned']}", 'success')

    if assignment_summary['failed_assignments']:
        flash(f"Failed to assign: {', '.join(assignment_summary['failed_assignments'][:5])}", 'warning')

    return redirect(url_for('assign_supervisor'))

@app.route('/admin/reassign-student/<int:student_id>', methods=['POST'])
@login_required
def reassign_student_supervisors(student_id):
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    student = Student.query.get_or_404(student_id)

    # Clear current assignments
    student.school_supervisor_id = None
    student.industry_supervisor_id = None

    # Auto-assign new supervisors
    assignment_results = auto_assign_supervisors(student)

    db.session.commit()

    # Flash assignment results
    for message in assignment_results['messages']:
        if 'assigned' in message:
            flash(message, 'success')
        else:
            flash(message, 'warning')

    return redirect(url_for('assign_supervisor'))

@app.route('/admin/supervisor-workload')
@login_required
def supervisor_workload():
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Get workload statistics for all supervisors
    school_supervisors = db.session.query(User, SchoolSupervisor)\
        .join(SchoolSupervisor, User.id == SchoolSupervisor.user_id)\
        .all()

    industry_supervisors = db.session.query(User, IndustrySupervisor)\
        .join(IndustrySupervisor, User.id == IndustrySupervisor.user_id)\
        .all()

    school_workload = []
    for user, profile in school_supervisors:
        workload = get_supervisor_workload(user.id, 'school')
        school_workload.append({
            'supervisor': user,
            'profile': profile,
            'workload': workload
        })

    industry_workload = []
    for user, profile in industry_supervisors:
        workload = get_supervisor_workload(user.id, 'industry')
        industry_workload.append({
            'supervisor': user,
            'profile': profile,
            'workload': workload
        })

    # Sort by workload
    school_workload.sort(key=lambda x: x['workload'])
    industry_workload.sort(key=lambda x: x['workload'])

    return render_template('admin/supervisor_workload.html',
                         school_workload=school_workload,
                         industry_workload=industry_workload)

@app.route('/admin/reports')
@login_required
def admin_reports():
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    # Generate comprehensive system statistics
    from sqlalchemy import func, extract

    # Basic counts
    total_users = User.query.count()
    total_students = Student.query.count()
    total_supervisors = User.query.filter(User.role_id.in_([2, 3, 4])).count()
    total_logs = LogEntry.query.count()

    # Log status breakdown
    approved_logs = LogEntry.query.filter_by(status='Approved').count()
    pending_logs = LogEntry.query.filter_by(status='Pending').count()
    rejected_logs = LogEntry.query.filter_by(status='Rejected').count()

    # Students by department
    dept_stats = db.session.query(Student.department, func.count(Student.id))\
                          .group_by(Student.department)\
                          .all()

    # Students by institution
    inst_stats = db.session.query(Student.institution, func.count(Student.id))\
                          .group_by(Student.institution)\
                          .all()

    # Students by state
    state_stats = db.session.query(Student.siwes_state, func.count(Student.id))\
                           .group_by(Student.siwes_state)\
                           .all()

    # Monthly registration trends (last 12 months)
    monthly_registrations = db.session.query(
        extract('year', User.created_at).label('year'),
        extract('month', User.created_at).label('month'),
        func.count(User.id).label('count')
    ).group_by(
        extract('year', User.created_at),
        extract('month', User.created_at)
    ).order_by(
        extract('year', User.created_at).desc(),
        extract('month', User.created_at).desc()
    ).limit(12).all()

    # Supervisor assignment statistics
    students_with_industry = Student.query.filter(Student.industry_supervisor_id.isnot(None)).count()
    students_with_school = Student.query.filter(Student.school_supervisor_id.isnot(None)).count()
    fully_assigned_students = Student.query.filter(
        Student.industry_supervisor_id.isnot(None),
        Student.school_supervisor_id.isnot(None)
    ).count()

    # Recent activity
    recent_users = User.query.order_by(User.created_at.desc()).limit(10).all()
    recent_logs = LogEntry.query.order_by(LogEntry.created_at.desc()).limit(10).all()

    return render_template('admin/reports.html',
                         total_users=total_users,
                         total_students=total_students,
                         total_supervisors=total_supervisors,
                         total_logs=total_logs,
                         approved_logs=approved_logs,
                         pending_logs=pending_logs,
                         rejected_logs=rejected_logs,
                         dept_stats=dept_stats,
                         inst_stats=inst_stats,
                         state_stats=state_stats,
                         monthly_registrations=monthly_registrations,
                         students_with_industry=students_with_industry,
                         students_with_school=students_with_school,
                         fully_assigned_students=fully_assigned_students,
                         recent_users=recent_users,
                         recent_logs=recent_logs)

@app.route('/admin/reports/export/<report_type>')
@login_required
def export_report(report_type):
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    if report_type == 'students_csv':
        return export_students_csv()
    elif report_type == 'logs_csv':
        return export_logs_csv()
    elif report_type == 'supervisors_csv':
        return export_supervisors_csv()
    elif report_type == 'system_summary_csv':
        return export_system_summary_csv()
    else:
        flash('Invalid report type!', 'error')
        return redirect(url_for('admin_reports'))

def export_students_csv():
    """Export all students data to CSV"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'ID', 'Name', 'Email', 'Matric Number', 'Department', 'Institution',
        'SIWES State', 'Organization', 'Start Date', 'End Date',
        'Industry Supervisor', 'School Supervisor', 'Total Logs',
        'Approved Logs', 'Pending Logs', 'Rejected Logs', 'Registration Date'
    ])

    # Get all students with their data - Fix ambiguous join by specifying the foreign key
    students = Student.query.join(User, Student.user_id == User.id).all()

    for student in students:
        # Calculate log statistics
        total_logs = LogEntry.query.filter_by(student_id=student.id).count()
        approved_logs = LogEntry.query.filter_by(student_id=student.id, status='Approved').count()
        pending_logs = LogEntry.query.filter_by(student_id=student.id, status='Pending').count()
        rejected_logs = LogEntry.query.filter_by(student_id=student.id, status='Rejected').count()

        writer.writerow([
            student.id,
            student.user.name,
            student.user.email,
            student.matric_number,
            student.department or 'N/A',
            student.institution or 'N/A',
            student.siwes_state or 'N/A',
            student.siwes_organization_name or 'N/A',
            student.siwes_start_date.strftime('%Y-%m-%d') if student.siwes_start_date else 'N/A',
            student.siwes_end_date.strftime('%Y-%m-%d') if student.siwes_end_date else 'N/A',
            student.industry_supervisor.name if student.industry_supervisor else 'N/A',
            student.school_supervisor.name if student.school_supervisor else 'N/A',
            total_logs,
            approved_logs,
            pending_logs,
            rejected_logs,
            student.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=students_report_{date.today().strftime("%Y%m%d")}.csv'

    return response

def export_logs_csv():
    """Export all log entries to CSV"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'ID', 'Student Name', 'Matric Number', 'Log Date', 'Activities',
        'Key Learnings', 'Status', 'Industry Review', 'School Review',
        'Media Files Count', 'Created Date'
    ])

    # Get all log entries - Fix ambiguous join by specifying the foreign keys
    logs = LogEntry.query.join(Student, LogEntry.student_id == Student.id)\
                         .join(User, Student.user_id == User.id).all()

    for log in logs:
        media_count = len(log.media_files) if log.media_files else 0

        writer.writerow([
            log.id,
            log.student.user.name,
            log.student.matric_number,
            log.log_date.strftime('%Y-%m-%d'),
            log.activities_performed[:100] + '...' if len(log.activities_performed) > 100 else log.activities_performed,
            log.key_learnings[:100] + '...' if log.key_learnings and len(log.key_learnings) > 100 else (log.key_learnings or 'N/A'),
            log.status,
            'Yes' if log.industry_review_comments else 'No',
            'Yes' if log.school_review_comments else 'No',
            media_count,
            log.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=logs_report_{date.today().strftime("%Y%m%d")}.csv'

    return response

def export_supervisors_csv():
    """Export all supervisors data to CSV"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'ID', 'Name', 'Email', 'Role', 'Department', 'Institution/Company',
        'Position', 'Specialization', 'Years Experience', 'Assigned Students',
        'Total Student Logs', 'Registration Date'
    ])

    # Get all supervisors
    supervisors = User.query.filter(User.role_id.in_([2, 3, 4])).all()

    for supervisor in supervisors:
        # Get supervisor profile
        profile = None
        dept_or_company = 'N/A'
        position = 'N/A'
        specialization = 'N/A'
        years_exp = 'N/A'

        if supervisor.role.name == 'School Supervisor':
            profile = SchoolSupervisor.query.filter_by(user_id=supervisor.id).first()
            if profile:
                dept_or_company = f"{profile.department}, {profile.institution}"
                position = profile.position or 'N/A'
                specialization = profile.specialization or 'N/A'
                years_exp = profile.years_of_experience or 'N/A'
        elif supervisor.role.name == 'Industry Supervisor':
            profile = IndustrySupervisor.query.filter_by(user_id=supervisor.id).first()
            if profile:
                dept_or_company = f"{profile.department}, {profile.company_name}"
                position = profile.position or 'N/A'
                specialization = profile.specialization or 'N/A'
                years_exp = profile.years_of_experience or 'N/A'
        elif supervisor.role.name == 'ITF Supervisor':
            profile = ITFSupervisor.query.filter_by(user_id=supervisor.id).first()
            if profile:
                dept_or_company = f"ITF - {profile.assigned_state}"
                position = profile.position or 'N/A'
                specialization = profile.specialization or 'N/A'
                years_exp = profile.years_of_experience or 'N/A'

        # Count assigned students and their logs
        assigned_students = 0
        total_student_logs = 0

        if supervisor.role.name == 'School Supervisor':
            assigned_students = Student.query.filter_by(school_supervisor_id=supervisor.id).count()
            student_ids = [s.id for s in Student.query.filter_by(school_supervisor_id=supervisor.id).all()]
            total_student_logs = LogEntry.query.filter(LogEntry.student_id.in_(student_ids)).count()
        elif supervisor.role.name == 'Industry Supervisor':
            assigned_students = Student.query.filter_by(industry_supervisor_id=supervisor.id).count()
            student_ids = [s.id for s in Student.query.filter_by(industry_supervisor_id=supervisor.id).all()]
            total_student_logs = LogEntry.query.filter(LogEntry.student_id.in_(student_ids)).count()

        writer.writerow([
            supervisor.id,
            supervisor.name,
            supervisor.email,
            supervisor.role.name,
            dept_or_company,
            position,
            specialization,
            years_exp,
            assigned_students,
            total_student_logs,
            supervisor.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=supervisors_report_{date.today().strftime("%Y%m%d")}.csv'

    return response

def export_system_summary_csv():
    """Export system summary statistics to CSV"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(['Metric', 'Value', 'Description'])

    # Calculate statistics
    total_users = User.query.count()
    total_students = Student.query.count()
    total_supervisors = User.query.filter(User.role_id.in_([2, 3, 4])).count()
    total_logs = LogEntry.query.count()
    approved_logs = LogEntry.query.filter_by(status='Approved').count()
    pending_logs = LogEntry.query.filter_by(status='Pending').count()
    rejected_logs = LogEntry.query.filter_by(status='Rejected').count()

    # Supervisor assignment stats
    students_with_industry = Student.query.filter(Student.industry_supervisor_id.isnot(None)).count()
    students_with_school = Student.query.filter(Student.school_supervisor_id.isnot(None)).count()
    fully_assigned = Student.query.filter(
        Student.industry_supervisor_id.isnot(None),
        Student.school_supervisor_id.isnot(None)
    ).count()

    # Write data
    writer.writerow(['Total Users', total_users, 'Total registered users in the system'])
    writer.writerow(['Total Students', total_students, 'Total registered students'])
    writer.writerow(['Total Supervisors', total_supervisors, 'Total supervisors (School + Industry + ITF)'])
    writer.writerow(['Total Log Entries', total_logs, 'Total log entries submitted'])
    writer.writerow(['Approved Logs', approved_logs, 'Log entries approved by supervisors'])
    writer.writerow(['Pending Logs', pending_logs, 'Log entries awaiting review'])
    writer.writerow(['Rejected Logs', rejected_logs, 'Log entries rejected by supervisors'])
    writer.writerow(['Students with Industry Supervisor', students_with_industry, 'Students assigned to industry supervisors'])
    writer.writerow(['Students with School Supervisor', students_with_school, 'Students assigned to school supervisors'])
    writer.writerow(['Fully Assigned Students', fully_assigned, 'Students with both industry and school supervisors'])

    # Approval rate
    if total_logs > 0:
        approval_rate = round((approved_logs / total_logs) * 100, 2)
        writer.writerow(['Log Approval Rate (%)', approval_rate, 'Percentage of logs approved'])

    # Assignment rate
    if total_students > 0:
        assignment_rate = round((fully_assigned / total_students) * 100, 2)
        writer.writerow(['Full Assignment Rate (%)', assignment_rate, 'Percentage of students with both supervisors'])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=system_summary_{date.today().strftime("%Y%m%d")}.csv'

    return response

# ITF Export Functions
def itf_export_students_csv(assigned_state):
    """Export students data for ITF supervisor's assigned state"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'ID', 'Name', 'Email', 'Matric Number', 'Department', 'Institution',
        'Organization', 'Start Date', 'End Date', 'Industry Supervisor',
        'School Supervisor', 'Total Logs', 'Approved Logs', 'Pending Logs',
        'Rejected Logs', 'Registration Date'
    ])

    # Get students in assigned state - Fix ambiguous join
    students = Student.query.join(User, Student.user_id == User.id).filter(Student.siwes_state == assigned_state).all()

    for student in students:
        # Calculate log statistics
        total_logs = LogEntry.query.filter_by(student_id=student.id).count()
        approved_logs = LogEntry.query.filter_by(student_id=student.id, status='Approved').count()
        pending_logs = LogEntry.query.filter_by(student_id=student.id, status='Pending').count()
        rejected_logs = LogEntry.query.filter_by(student_id=student.id, status='Rejected').count()

        writer.writerow([
            student.id,
            student.user.name,
            student.user.email,
            student.matric_number,
            student.department or 'N/A',
            student.institution or 'N/A',
            student.siwes_organization_name or 'N/A',
            student.siwes_start_date.strftime('%Y-%m-%d') if student.siwes_start_date else 'N/A',
            student.siwes_end_date.strftime('%Y-%m-%d') if student.siwes_end_date else 'N/A',
            student.industry_supervisor.name if student.industry_supervisor else 'N/A',
            student.school_supervisor.name if student.school_supervisor else 'N/A',
            total_logs,
            approved_logs,
            pending_logs,
            rejected_logs,
            student.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=itf_students_{assigned_state}_{date.today().strftime("%Y%m%d")}.csv'

    return response

def itf_export_logs_csv(assigned_state):
    """Export log entries for students in ITF supervisor's assigned state"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'ID', 'Student Name', 'Matric Number', 'Department', 'Log Date',
        'Activities', 'Key Learnings', 'Status', 'Industry Review',
        'School Review', 'Media Files Count', 'Created Date'
    ])

    # Get log entries for students in assigned state - Fix ambiguous join
    logs = LogEntry.query.join(Student, LogEntry.student_id == Student.id)\
                         .join(User, Student.user_id == User.id)\
                         .filter(Student.siwes_state == assigned_state)\
                         .order_by(LogEntry.created_at.desc()).all()

    for log in logs:
        media_count = len(log.media_files) if log.media_files else 0

        writer.writerow([
            log.id,
            log.student.user.name,
            log.student.matric_number,
            log.student.department or 'N/A',
            log.log_date.strftime('%Y-%m-%d'),
            log.activities_performed[:100] + '...' if len(log.activities_performed) > 100 else log.activities_performed,
            log.key_learnings[:100] + '...' if log.key_learnings and len(log.key_learnings) > 100 else (log.key_learnings or 'N/A'),
            log.status,
            'Yes' if log.industry_review_comments else 'No',
            'Yes' if log.school_review_comments else 'No',
            media_count,
            log.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=itf_logs_{assigned_state}_{date.today().strftime("%Y%m%d")}.csv'

    return response

def itf_export_summary_csv(assigned_state):
    """Export summary statistics for ITF supervisor's assigned state"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(['Metric', 'Value', 'Description'])

    # Calculate statistics for assigned state
    state_students = Student.query.filter_by(siwes_state=assigned_state)
    total_state_students = state_students.count()

    # Log statistics
    state_student_ids = [s.id for s in state_students.all()]
    if state_student_ids:
        state_logs = LogEntry.query.filter(LogEntry.student_id.in_(state_student_ids))
        total_state_logs = state_logs.count()
        approved_state_logs = state_logs.filter_by(status='Approved').count()
        pending_state_logs = state_logs.filter_by(status='Pending').count()
        rejected_state_logs = state_logs.filter_by(status='Rejected').count()
    else:
        total_state_logs = approved_state_logs = pending_state_logs = rejected_state_logs = 0

    # Supervisor assignment statistics
    students_with_industry = state_students.filter(Student.industry_supervisor_id.isnot(None)).count()
    students_with_school = state_students.filter(Student.school_supervisor_id.isnot(None)).count()
    fully_assigned = state_students.filter(
        Student.industry_supervisor_id.isnot(None),
        Student.school_supervisor_id.isnot(None)
    ).count()

    # Write data
    writer.writerow(['State', assigned_state, f'ITF supervision area'])
    writer.writerow(['Total Students', total_state_students, f'Students registered in {assigned_state}'])
    writer.writerow(['Total Log Entries', total_state_logs, 'Log entries submitted by state students'])
    writer.writerow(['Approved Logs', approved_state_logs, 'Log entries approved by supervisors'])
    writer.writerow(['Pending Logs', pending_state_logs, 'Log entries awaiting review'])
    writer.writerow(['Rejected Logs', rejected_state_logs, 'Log entries rejected by supervisors'])
    writer.writerow(['Students with Industry Supervisor', students_with_industry, 'Students assigned to industry supervisors'])
    writer.writerow(['Students with School Supervisor', students_with_school, 'Students assigned to school supervisors'])
    writer.writerow(['Fully Assigned Students', fully_assigned, 'Students with both industry and school supervisors'])

    # Calculate rates
    if total_state_logs > 0:
        approval_rate = round((approved_state_logs / total_state_logs) * 100, 2)
        writer.writerow(['Log Approval Rate (%)', approval_rate, 'Percentage of logs approved'])

    if total_state_students > 0:
        assignment_rate = round((fully_assigned / total_state_students) * 100, 2)
        writer.writerow(['Full Assignment Rate (%)', assignment_rate, 'Percentage of students with both supervisors'])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=itf_summary_{assigned_state}_{date.today().strftime("%Y%m%d")}.csv'

    return response

# Supervisor Export Functions
def supervisor_export_students_csv():
    """Export students data for current supervisor"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'ID', 'Name', 'Email', 'Matric Number', 'Department', 'Institution',
        'Organization', 'Start Date', 'End Date', 'Total Logs', 'Approved Logs',
        'Pending Logs', 'Rejected Logs', 'Registration Date'
    ])

    # Get students assigned to current supervisor - Fix ambiguous join
    if current_user.role.name == 'Industry Supervisor':
        students = Student.query.join(User, Student.user_id == User.id).filter(Student.industry_supervisor_id == current_user.id).all()
        filename_prefix = 'industry_supervisor'
    else:
        students = Student.query.join(User, Student.user_id == User.id).filter(Student.school_supervisor_id == current_user.id).all()
        filename_prefix = 'school_supervisor'

    for student in students:
        # Calculate log statistics
        total_logs = LogEntry.query.filter_by(student_id=student.id).count()
        approved_logs = LogEntry.query.filter_by(student_id=student.id, status='Approved').count()
        pending_logs = LogEntry.query.filter_by(student_id=student.id, status='Pending').count()
        rejected_logs = LogEntry.query.filter_by(student_id=student.id, status='Rejected').count()

        writer.writerow([
            student.id,
            student.user.name,
            student.user.email,
            student.matric_number,
            student.department or 'N/A',
            student.institution or 'N/A',
            student.siwes_organization_name or 'N/A',
            student.siwes_start_date.strftime('%Y-%m-%d') if student.siwes_start_date else 'N/A',
            student.siwes_end_date.strftime('%Y-%m-%d') if student.siwes_end_date else 'N/A',
            total_logs,
            approved_logs,
            pending_logs,
            rejected_logs,
            student.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename={filename_prefix}_students_{date.today().strftime("%Y%m%d")}.csv'

    return response

def supervisor_export_logs_csv():
    """Export log entries for current supervisor's students"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow([
        'ID', 'Student Name', 'Matric Number', 'Department', 'Log Date',
        'Activities', 'Key Learnings', 'Status', 'My Review', 'Other Review',
        'Media Files Count', 'Created Date'
    ])

    # Get log entries for supervisor's students - Fix ambiguous join
    if current_user.role.name == 'Industry Supervisor':
        logs = LogEntry.query.join(Student, LogEntry.student_id == Student.id)\
                             .join(User, Student.user_id == User.id)\
                             .filter(Student.industry_supervisor_id == current_user.id)\
                             .order_by(LogEntry.created_at.desc()).all()
        filename_prefix = 'industry_supervisor'
        my_review_field = 'industry_review_comments'
        other_review_field = 'school_review_comments'
    else:
        logs = LogEntry.query.join(Student, LogEntry.student_id == Student.id)\
                             .join(User, Student.user_id == User.id)\
                             .filter(Student.school_supervisor_id == current_user.id)\
                             .order_by(LogEntry.created_at.desc()).all()
        filename_prefix = 'school_supervisor'
        my_review_field = 'school_review_comments'
        other_review_field = 'industry_review_comments'

    for log in logs:
        media_count = len(log.media_files) if log.media_files else 0
        my_review = getattr(log, my_review_field)
        other_review = getattr(log, other_review_field)

        writer.writerow([
            log.id,
            log.student.user.name,
            log.student.matric_number,
            log.student.department or 'N/A',
            log.log_date.strftime('%Y-%m-%d'),
            log.activities_performed[:100] + '...' if len(log.activities_performed) > 100 else log.activities_performed,
            log.key_learnings[:100] + '...' if log.key_learnings and len(log.key_learnings) > 100 else (log.key_learnings or 'N/A'),
            log.status,
            'Yes' if my_review else 'No',
            'Yes' if other_review else 'No',
            media_count,
            log.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename={filename_prefix}_logs_{date.today().strftime("%Y%m%d")}.csv'

    return response

def supervisor_export_summary_csv():
    """Export summary statistics for current supervisor"""
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(['Metric', 'Value', 'Description'])

    # Get students assigned to current supervisor
    if current_user.role.name == 'Industry Supervisor':
        students = Student.query.filter_by(industry_supervisor_id=current_user.id).all()
        supervisor_type = 'Industry Supervisor'
        filename_prefix = 'industry_supervisor'
    else:
        students = Student.query.filter_by(school_supervisor_id=current_user.id).all()
        supervisor_type = 'School Supervisor'
        filename_prefix = 'school_supervisor'

    total_students = len(students)

    # Calculate log statistics
    all_logs = []
    for student in students:
        logs = LogEntry.query.filter_by(student_id=student.id).all()
        all_logs.extend(logs)

    total_logs = len(all_logs)
    approved_logs = len([log for log in all_logs if log.status == 'Approved'])
    pending_logs = len([log for log in all_logs if log.status == 'Pending'])
    rejected_logs = len([log for log in all_logs if log.status == 'Rejected'])

    # Write data
    writer.writerow(['Supervisor Type', supervisor_type, f'Role in SIWES system'])
    writer.writerow(['Supervisor Name', current_user.name, f'Logged in supervisor'])
    writer.writerow(['Assigned Students', total_students, f'Students assigned to this supervisor'])
    writer.writerow(['Total Log Entries', total_logs, 'Log entries from assigned students'])
    writer.writerow(['Approved Logs', approved_logs, 'Log entries approved'])
    writer.writerow(['Pending Logs', pending_logs, 'Log entries awaiting review'])
    writer.writerow(['Rejected Logs', rejected_logs, 'Log entries rejected'])

    # Calculate rates
    if total_logs > 0:
        approval_rate = round((approved_logs / total_logs) * 100, 2)
        writer.writerow(['Approval Rate (%)', approval_rate, 'Percentage of logs approved'])

        avg_logs_per_student = round(total_logs / total_students, 2) if total_students > 0 else 0
        writer.writerow(['Avg Logs per Student', avg_logs_per_student, 'Average log entries per student'])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename={filename_prefix}_summary_{date.today().strftime("%Y%m%d")}.csv'

    return response

@app.route('/admin/user/<int:user_id>')
@login_required
def admin_view_user(user_id):
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)

    # Get user's profile based on role
    profile = None
    if user.role.name == 'Student':
        profile = Student.query.filter_by(user_id=user.id).first()
    elif user.role.name == 'School Supervisor':
        profile = SchoolSupervisor.query.filter_by(user_id=user.id).first()
    elif user.role.name == 'Industry Supervisor':
        profile = IndustrySupervisor.query.filter_by(user_id=user.id).first()
    elif user.role.name == 'ITF Supervisor':
        profile = ITFSupervisor.query.filter_by(user_id=user.id).first()

    # Get additional statistics and assigned students
    stats = {}
    assigned_students = []

    if user.role.name == 'Student' and profile:
        stats['total_logs'] = LogEntry.query.filter_by(student_id=profile.id).count()
        stats['approved_logs'] = LogEntry.query.filter_by(student_id=profile.id, status='Approved').count()
        stats['pending_logs'] = LogEntry.query.filter_by(student_id=profile.id, status='Pending').count()
        stats['rejected_logs'] = LogEntry.query.filter_by(student_id=profile.id, status='Rejected').count()

        # Get recent log entries for students
        recent_logs = LogEntry.query.filter_by(student_id=profile.id)\
                                   .order_by(LogEntry.log_date.desc())\
                                   .limit(5).all()
        stats['recent_logs'] = recent_logs

    elif user.role.name in ['School Supervisor', 'Industry Supervisor']:
        if user.role.name == 'School Supervisor':
            assigned_students_query = Student.query.filter_by(school_supervisor_id=user.id)
        else:
            assigned_students_query = Student.query.filter_by(industry_supervisor_id=user.id)

        assigned_students = assigned_students_query.all()
        stats['assigned_students'] = len(assigned_students)

        # Get detailed statistics for assigned students
        if assigned_students:
            total_logs = 0
            approved_logs = 0
            pending_logs = 0
            rejected_logs = 0

            for student in assigned_students:
                student_logs = LogEntry.query.filter_by(student_id=student.id).all()
                total_logs += len(student_logs)
                approved_logs += len([log for log in student_logs if log.status == 'Approved'])
                pending_logs += len([log for log in student_logs if log.status == 'Pending'])
                rejected_logs += len([log for log in student_logs if log.status == 'Rejected'])

            stats['total_student_logs'] = total_logs
            stats['approved_student_logs'] = approved_logs
            stats['pending_student_logs'] = pending_logs
            stats['rejected_student_logs'] = rejected_logs

            # Group students by institution and department for summary
            institution_summary = {}
            department_summary = {}

            for student in assigned_students:
                # Institution summary
                inst = student.institution or 'Unknown'
                if inst not in institution_summary:
                    institution_summary[inst] = 0
                institution_summary[inst] += 1

                # Department summary
                dept = student.department or 'Unknown'
                if dept not in department_summary:
                    department_summary[dept] = 0
                department_summary[dept] += 1

            stats['institution_summary'] = institution_summary
            stats['department_summary'] = department_summary

    elif user.role.name == 'ITF Supervisor' and profile:
        # Get students from ITF supervisor's assigned state
        state_students = Student.query.filter_by(siwes_state=profile.assigned_state).all()
        stats['state_students'] = len(state_students)

        # Get summary by institution in the state
        institution_summary = {}
        for student in state_students:
            inst = student.institution or 'Unknown'
            if inst not in institution_summary:
                institution_summary[inst] = 0
            institution_summary[inst] += 1
        stats['state_institution_summary'] = institution_summary

    return render_template('admin/user_details.html',
                         user=user,
                         profile=profile,
                         stats=stats,
                         assigned_students=assigned_students)

@app.route('/admin/user/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def admin_edit_user(user_id):
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        # Update basic user information
        user.name = request.form['name']
        user.email = request.form['email']
        user.phone = request.form.get('phone', '')

        # Update password if provided
        new_password = request.form.get('password')
        if new_password:
            user.password_hash = generate_password_hash(new_password)

        # Update role if changed
        new_role_id = request.form.get('role_id', type=int)
        if new_role_id and new_role_id != user.role_id:
            user.role_id = new_role_id

        # Update active status
        user.is_active = 'is_active' in request.form

        try:
            db.session.commit()
            flash('User updated successfully!', 'success')
            return redirect(url_for('admin_view_user', user_id=user.id))
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating user: {str(e)}', 'error')

    # Get all roles for the dropdown
    roles = Role.query.all()

    return render_template('admin/edit_user.html', user=user, roles=roles)

@app.route('/admin/user/<int:user_id>/toggle-status', methods=['POST'])
@login_required
def admin_toggle_user_status(user_id):
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)

    # Prevent admin from deactivating themselves
    if user.id == current_user.id:
        flash('You cannot deactivate your own account!', 'error')
        return redirect(url_for('admin_view_user', user_id=user.id))

    user.is_active = not user.is_active
    db.session.commit()

    status = 'activated' if user.is_active else 'deactivated'
    flash(f'User {status} successfully!', 'success')

    return redirect(url_for('admin_view_user', user_id=user.id))

@app.route('/admin/user/<int:user_id>/delete', methods=['POST'])
@login_required
def admin_delete_user(user_id):
    if current_user.role.name != 'Admin':
        flash('Access denied!', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)

    # Prevent admin from deleting themselves
    if user.id == current_user.id:
        flash('You cannot delete your own account!', 'error')
        return redirect(url_for('admin_view_user', user_id=user.id))

    try:
        # Delete related profile data first
        if user.role.name == 'Student':
            student = Student.query.filter_by(user_id=user.id).first()
            if student:
                # Delete log entries and media files
                for log in student.log_entries:
                    for media in log.media_files:
                        # Delete physical file
                        file_path = os.path.join(app.config['UPLOAD_FOLDER'], media.filename)
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        db.session.delete(media)
                    db.session.delete(log)
                db.session.delete(student)
        elif user.role.name == 'School Supervisor':
            profile = SchoolSupervisor.query.filter_by(user_id=user.id).first()
            if profile:
                db.session.delete(profile)
        elif user.role.name == 'Industry Supervisor':
            profile = IndustrySupervisor.query.filter_by(user_id=user.id).first()
            if profile:
                db.session.delete(profile)
        elif user.role.name == 'ITF Supervisor':
            profile = ITFSupervisor.query.filter_by(user_id=user.id).first()
            if profile:
                db.session.delete(profile)

        # Delete the user
        db.session.delete(user)
        db.session.commit()

        flash('User deleted successfully!', 'success')
        return redirect(url_for('admin_users'))

    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting user: {str(e)}', 'error')
        return redirect(url_for('admin_view_user', user_id=user.id))

if __name__ == '__main__':
    with app.app_context():
        # Create all database tables
        db.create_all()

        # Create default roles if they don't exist
        if not Role.query.first():
            roles = [
                Role(name='Student', description='SIWES Student'),
                Role(name='Industry Supervisor', description='Industry-based supervisor'),
                Role(name='School Supervisor', description='School-based supervisor'),
                Role(name='ITF Supervisor', description='ITF oversight supervisor'),
                Role(name='Admin', description='System administrator')
            ]
            for role in roles:
                db.session.add(role)
            db.session.commit()
            print("✅ Default roles created!")

        print("✅ Database tables created!")

    print("🚀 Starting SIWES Application...")
    print("📱 Access at: http://localhost:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
