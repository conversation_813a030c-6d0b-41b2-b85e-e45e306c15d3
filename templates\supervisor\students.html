{% extends "base.html" %}

{% block title %}My Students - {{ supervisor_type }} - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>My Students
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-primary">{{ students|length }} Students</span>
        </div>
    </div>
</div>

{% if students %}
<div class="row">
    {% for student in students %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-user-graduate me-2"></i>{{ student.user.name }}
                </h6>
            </div>
            <div class="card-body">
                <p class="card-text">
                    <strong>Matric Number:</strong> {{ student.matric_number }}<br>
                    <strong>Department:</strong> {{ student.department or 'Not specified' }}<br>
                    <strong>Institution:</strong> {{ student.institution or 'Not specified' }}<br>
                    <strong>Organization:</strong> {{ student.siwes_organization_name or 'Not specified' }}
                </p>

                {% if student.siwes_start_date and student.siwes_end_date %}
                <p class="card-text">
                    <strong>SIWES Period:</strong><br>
                    <small class="text-muted">
                        {{ student.siwes_start_date.strftime('%Y-%m-%d') }} to
                        {{ student.siwes_end_date.strftime('%Y-%m-%d') }}
                    </small>
                </p>
                {% endif %}

                <!-- Quick Stats -->
                {% set student_logs = student.log_entries %}
                {% set total_logs = student_logs|length %}
                {% set pending_logs = student_logs|selectattr('status', 'equalto', 'Pending')|list|length %}
                {% set approved_logs = student_logs|selectattr('status', 'equalto', 'Approved')|list|length %}

                <div class="row text-center mt-3">
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-primary">{{ total_logs }}</h6>
                            <small class="text-muted">Total Logs</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-warning">{{ pending_logs }}</h6>
                            <small class="text-muted">Pending</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="text-success">{{ approved_logs }}</h6>
                        <small class="text-muted">Approved</small>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('view_student_details', student_id=student.id) }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-2"></i>View Details
                    </a>
                    {% if supervisor_type == 'School Supervisor' %}
                    <a href="{{ url_for('add_monthly_comment', student_id=student.id) }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-calendar-alt me-2"></i>Monthly Comment
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Summary Statistics -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x text-primary mb-3"></i>
                <h5>{{ students|length }}</h5>
                <p class="text-muted">Total Students Assigned</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                <h5>{{ count_logs_by_status(students, 'Pending') }}</h5>
                <p class="text-muted">Pending Reviews</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                <h5>{{ count_logs_by_status(students, 'Approved') }}</h5>
                <p class="text-muted">Approved Logs</p>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-users fa-4x text-muted mb-4"></i>
        <h4 class="text-muted">No Students Assigned</h4>
        <p class="text-muted mb-4">
            You don't have any students assigned to you yet.
            Contact the system administrator to assign students to your supervision.
        </p>
        <div class="alert alert-info">
            <strong>Note:</strong> Students need to be assigned to you by an administrator
            before you can review their log entries and monitor their progress.
        </div>
    </div>
</div>
{% endif %}

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Supervisor Guidelines
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Review Responsibilities</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Review log entries promptly</li>
                            <li><i class="fas fa-check text-success me-2"></i>Provide constructive feedback</li>
                            <li><i class="fas fa-check text-success me-2"></i>Monitor student progress</li>
                            <li><i class="fas fa-check text-success me-2"></i>Guide learning objectives</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Best Practices</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Regular communication</li>
                            <li><i class="fas fa-check text-success me-2"></i>Timely feedback</li>
                            <li><i class="fas fa-check text-success me-2"></i>Encourage reflection</li>
                            <li><i class="fas fa-check text-success me-2"></i>Support skill development</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
