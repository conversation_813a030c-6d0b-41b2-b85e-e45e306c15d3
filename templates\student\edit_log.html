{% extends "base.html" %}

{% block title %}Edit Log Entry - Student Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('student_logs') }}">
                    <i class="fas fa-clipboard-list me-2"></i>My Logs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('new_log_entry') }}">
                    <i class="fas fa-plus me-2"></i>New Log Entry
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('view_school_supervisor') }}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>School Supervisor
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>Edit Log Entry
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('student_logs') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Logs
            </a>
        </div>
    </div>
</div>

<!-- Status Alert -->
<div class="alert alert-info" role="alert">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Editing {{ log_entry.status }} Log Entry</strong><br>
    You can edit and resubmit this log entry. Once updated, it will be reset to "Pending" status for supervisor review.
    {% if log_entry.status == 'Rejected' and log_entry.industry_review_comments %}
        <br><strong>Rejection Reason:</strong> {{ log_entry.industry_review_comments }}
    {% endif %}
    {% if log_entry.status == 'Rejected' and log_entry.school_review_comments %}
        <br><strong>School Supervisor Feedback:</strong> {{ log_entry.school_review_comments }}
    {% endif %}
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar me-2"></i>Log Entry for {{ log_entry.log_date.strftime('%B %d, %Y') }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="log_date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="log_date" name="log_date"
                               value="{{ log_entry.log_date.strftime('%Y-%m-%d') }}" readonly>
                        <div class="form-text">Log date cannot be changed when editing</div>
                    </div>

                    <div class="mb-3">
                        <label for="activities_performed" class="form-label">Activities Performed <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="activities_performed" name="activities_performed"
                                  rows="6" required placeholder="Describe the activities you performed on this day...">{{ log_entry.activities_performed }}</textarea>
                        <div class="form-text">Provide detailed description of your daily activities</div>
                    </div>

                    <div class="mb-3">
                        <label for="key_learnings" class="form-label">Key Learnings</label>
                        <textarea class="form-control" id="key_learnings" name="key_learnings"
                                  rows="4" placeholder="What did you learn from today's activities?">{{ log_entry.key_learnings or '' }}</textarea>
                        <div class="form-text">Describe what you learned or skills you developed</div>
                    </div>

                    <div class="mb-3">
                        <label for="media_files" class="form-label">Additional Files (Optional)</label>
                        <input type="file" class="form-control" id="media_files" name="media_files"
                               multiple accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.mp4,.avi,.mov,.wmv,.flv,.pdf,.doc,.docx">
                        <div class="form-text">
                            Upload images, videos, or documents related to your activities.
                            Supported formats: Images (JPG, PNG, GIF, BMP, WEBP), Videos (MP4, AVI, MOV, WMV, FLV), Documents (PDF, DOC, DOCX)
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('student_logs') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update & Resubmit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Current Status -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Current Status
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    {% if log_entry.status == 'Rejected' %}
                        <span class="badge bg-danger fs-6 mb-2">{{ log_entry.status }}</span>
                    {% elif log_entry.status == 'Pending' %}
                        <span class="badge bg-warning fs-6 mb-2">{{ log_entry.status }}</span>
                    {% else %}
                        <span class="badge bg-success fs-6 mb-2">{{ log_entry.status }}</span>
                    {% endif %}
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <strong>Created:</strong> {{ log_entry.created_at.strftime('%Y-%m-%d %H:%M') }}<br>
                        {% if log_entry.updated_at %}
                        <strong>Last Updated:</strong> {{ log_entry.updated_at.strftime('%Y-%m-%d %H:%M') }}<br>
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>

        <!-- Previous Reviews -->
        {% if log_entry.industry_review_comments or log_entry.school_review_comments %}
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-comments me-2"></i>Previous Reviews
                </h6>
            </div>
            <div class="card-body">
                {% if log_entry.industry_review_comments %}
                <div class="mb-3">
                    <strong class="text-primary">Industry Supervisor:</strong>
                    <p class="mb-1">{{ log_entry.industry_review_comments }}</p>
                    {% if log_entry.industry_review_date %}
                    <small class="text-muted">{{ log_entry.industry_review_date.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% endif %}
                </div>
                {% endif %}

                {% if log_entry.school_review_comments %}
                <div class="mb-3">
                    <strong class="text-info">School Supervisor:</strong>
                    <p class="mb-1">{{ log_entry.school_review_comments }}</p>
                    {% if log_entry.school_review_date %}
                    <small class="text-muted">{{ log_entry.school_review_date.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Help -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>Help
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>Tips for editing:</strong><br>
                    • Address any feedback from supervisors<br>
                    • Provide more detailed descriptions<br>
                    • Add specific examples of your work<br>
                    • Include relevant learning outcomes<br>
                    • Attach supporting documents if helpful
                </small>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-resize textareas
document.addEventListener('DOMContentLoaded', function() {
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(function(textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
        // Initial resize
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    });
});
</script>
{% endblock %}