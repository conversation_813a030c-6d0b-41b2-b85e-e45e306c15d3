# CHAPTER FOUR

## SY<PERSON><PERSON> IMPLEMENTATION AND RESULTS

### 4.1. Introduction

This chapter details the implementation of the Online Industrial Training Logbook System. It describes the tools and technologies used, the development of the various modules, and the results obtained from testing the system. The implementation phase translates the design specifications from Chapter Three into a functional and operational system.

### 4.2. System Implementation

The system was implemented using the technologies outlined in the previous chapter. The backend was developed using the Flask web framework in Python, and the frontend was built with HTML, CSS, and JavaScript, using the Jinja2 templating engine provided by Flask.

#### 4.2.1. Development Environment

The following tools were used during the development of the project:
*   **Text Editor:** Visual Studio Code
*   **Programming Language:** Python 3.8
*   **Web Framework:** Flask 2.0
*   **Database:** SQLite
*   **Version Control:** Git

#### 4.2.2. Backend Implementation

The backend is the core of the application, handling all business logic, user authentication, and database interactions. The `app.py` file contains the main Flask application, defining the routes and the logic for each page.

Key implemented modules include:
*   **User Authentication:** Secure registration and login functionality for all user roles (student, supervisor, admin). Passwords are hashed for security.
*   **Logbook Management:** CRUD (Create, Read, Update, Delete) operations for student logbook entries.
*   **Supervisor Review:** Functionality for supervisors to review and comment on student logs.
*   **Admin Dashboard:** A central dashboard for administrators to manage users, assign supervisors, and view system-wide reports.

#### 4.2.3. Frontend Implementation

The frontend was implemented using HTML templates located in the `templates/` directory. The templates are organized into subdirectories based on user roles (`admin/`, `student/`, `supervisor/`, `itf/`). A base template (`base.html`) is used to maintain a consistent layout across the application.

Key frontend components include:
*   **Login and Registration Pages:** Simple forms for user authentication.
*   **Dashboards:** Role-specific dashboards that provide users with an overview of their tasks and information.
*   **Logbook Pages:** Forms for creating and editing log entries, and pages for viewing logs and comments.
*   **User Management Pages:** Tables and forms for administrators to manage system users.

### 4.3. System Testing

The system was tested thoroughly to ensure its functionality, reliability, and usability. The testing process involved unit testing, integration testing, and user acceptance testing.

*   **Unit Testing:** Individual components, such as database models and authentication functions, were tested in isolation.
*   **Integration Testing:** The interaction between different modules, such as the frontend and backend, was tested to ensure they work together as expected.
*   **User Acceptance Testing (UAT):** The system was tested from the perspective of the end-users (students, supervisors, and administrators) to ensure it meets their requirements.

### 4.4. Results and Discussion of Results

The implemented system successfully meets the objectives outlined in Chapter One. The application provides a user-friendly interface for all stakeholders to manage the industrial training logbook process efficiently.

#### 4.4.1. User Authentication

The system provides secure login and registration for all users.

**(Insert Screenshot of Login Page Here)**
**Figure 4.1: Login Page**

#### 4.4.2. Student Dashboard

After logging in, students are directed to their dashboard where they can manage their profile, view their logbook, and see comments from their supervisor.

**(Insert Screenshot of Student Dashboard Here)**
**Figure 4.2: Student Dashboard**

#### 4.4.3. Logbook Submission

Students can easily create and submit their weekly logbook entries through a simple form.

**(Insert Screenshot of New Log Entry Page Here)**
**Figure 4.3: New Log Entry Page**

#### 4.4.4. Supervisor Dashboard and Review

Supervisors have a dedicated dashboard to view the students they are supervising and review their logbook entries. They can add comments and approve or reject entries.

**(Insert Screenshot of Supervisor Dashboard Here)**
**Figure 4.4: Supervisor Dashboard**

**(Insert Screenshot of Logbook Review Page Here)**
**Figure 4.5: Logbook Review Page**

#### 4.4.5. Admin Dashboard

The admin dashboard provides a comprehensive overview of the system, allowing administrators to manage users, assign supervisors, and generate reports.

**(Insert Screenshot of Admin Dashboard - User Management Here)**
**Figure 4.6: Admin Dashboard - User Management**

### 4.5. Performance Evaluation

The system's performance was evaluated based on its response time and resource utilization. The application is lightweight and responds quickly to user requests. The use of SQLite as the database ensures efficient data retrieval. The system is capable of handling a moderate number of concurrent users, which is sufficient for the context of a university department.

### 4.6. Hardware and Software Requirements

#### 4.6.1. Hardware Requirements (Minimum)
*   **Processor:** 1 GHz
*   **RAM:** 512 MB
*   **Hard Disk:** 100 MB of free space

#### 4.6.2. Software Requirements
*   **Operating System:** Windows, macOS, or Linux
*   **Web Browser:** Google Chrome, Mozilla Firefox, or Microsoft Edge
*   **Python:** Version 3.6 or higher
*   **Flask:** Version 1.1 or higher

### 4.7. References

*   Black, P. E. (2021). *Unit Testing*. In Dictionary of Algorithms and Data Structures. National Institute of Standards and Technology.
*   Pressman, R. S., & Maxim, B. R. (2020). *Software Engineering: A Practitioner's Approach*. McGraw-Hill Education.
