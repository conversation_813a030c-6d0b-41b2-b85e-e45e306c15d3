{% extends "base.html" %}

{% block title %}{{ supervisor_type }} Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-tie me-2"></i>{{ supervisor_type }} Dashboard
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('supervisor_reviews') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-clipboard-check me-1"></i>Review Logs
            </a>
            <a href="{{ url_for('supervisor_reports') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-chart-bar me-1"></i>View Reports
            </a>
        </div>
    </div>
</div>

    <div class="row">
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-primary stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Assigned Students</h5>
                            <h2 class="mb-0">{{ students|length }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card text-white bg-warning stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Pending Reviews</h5>
                            <h2 class="mb-0">{{ pending_logs|length }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card text-white bg-success stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Reviews Completed</h5>
                            <h2 class="mb-0">0</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card text-white bg-info stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Active Students</h5>
                            <h2 class="mb-0">{{ students|length }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-graduate fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Pending Log Reviews
                    </h5>
                </div>
                <div class="card-body">
                    {% if pending_logs %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Date</th>
                                        <th>Activities</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for log in pending_logs %}
                                    <tr>
                                        <td>{{ log.student.user.name }}</td>
                                        <td>{{ log.log_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ log.activities_performed[:50] }}{% if log.activities_performed|length > 50 %}...{% endif %}</td>
                                        <td>
                                            <a href="{{ url_for('review_log_entry', log_id=log.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye me-1"></i>Review
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-muted">No pending reviews</h5>
                            <p class="text-muted">All log entries have been reviewed!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>Assigned Students
                    </h5>
                </div>
                <div class="card-body">
                    {% if students %}
                        {% for student in students %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ student.user.name }}</strong><br>
                                <small class="text-muted">{{ student.matric_number }}</small>
                            </div>
                            <a href="{{ url_for('view_student_details', student_id=student.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                        </div>
                        <hr>
                        {% endfor %}
                    {% else %}
                        <div class="text-center">
                            <i class="fas fa-user-plus fa-2x text-muted mb-3"></i>
                            <p class="text-muted">No students assigned yet</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
