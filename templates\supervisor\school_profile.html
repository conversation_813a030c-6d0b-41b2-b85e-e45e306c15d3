{% extends "base.html" %}

{% block title %}School Supervisor Profile - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('supervisor_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>School Supervisor Profile
    </h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-university me-2"></i>Academic Information
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="institution" class="form-label">Institution <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="institution" name="institution" 
                                   value="{{ profile.institution if profile else '' }}" required
                                   placeholder="e.g., University of Lagos">
                            <div class="form-text">The institution where you work</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="department" class="form-label">Department <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="department" name="department" 
                                   value="{{ profile.department if profile else '' }}" required
                                   placeholder="e.g., Computer Science">
                            <div class="form-text">Your academic department</div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="faculty" class="form-label">Faculty</label>
                            <input type="text" class="form-control" id="faculty" name="faculty" 
                                   value="{{ profile.faculty if profile else '' }}"
                                   placeholder="e.g., Faculty of Science">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="position" class="form-label">Position</label>
                            <select class="form-select" id="position" name="position">
                                <option value="">Select Position</option>
                                <option value="Graduate Assistant" {{ 'selected' if profile and profile.position == 'Graduate Assistant' else '' }}>Graduate Assistant</option>
                                <option value="Assistant Lecturer" {{ 'selected' if profile and profile.position == 'Assistant Lecturer' else '' }}>Assistant Lecturer</option>
                                <option value="Lecturer II" {{ 'selected' if profile and profile.position == 'Lecturer II' else '' }}>Lecturer II</option>
                                <option value="Lecturer I" {{ 'selected' if profile and profile.position == 'Lecturer I' else '' }}>Lecturer I</option>
                                <option value="Senior Lecturer" {{ 'selected' if profile and profile.position == 'Senior Lecturer' else '' }}>Senior Lecturer</option>
                                <option value="Associate Professor" {{ 'selected' if profile and profile.position == 'Associate Professor' else '' }}>Associate Professor</option>
                                <option value="Professor" {{ 'selected' if profile and profile.position == 'Professor' else '' }}>Professor</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="office_location" class="form-label">Office Location</label>
                        <input type="text" class="form-control" id="office_location" name="office_location" 
                               value="{{ profile.office_location if profile else '' }}"
                               placeholder="e.g., Room 205, Faculty Building">
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="specialization" class="form-label">Area of Specialization</label>
                            <input type="text" class="form-control" id="specialization" name="specialization" 
                                   value="{{ profile.specialization if profile else '' }}"
                                   placeholder="e.g., Software Engineering, Data Science">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="years_of_experience" class="form-label">Years of Experience</label>
                            <input type="number" class="form-control" id="years_of_experience" name="years_of_experience" 
                                   value="{{ profile.years_of_experience if profile else '' }}" min="0" max="50">
                        </div>
                    </div>

                    <!-- Digital Signature Upload -->
                    <div class="mb-3">
                        <label for="signature_file" class="form-label">Digital Signature</label>
                        <input type="file" class="form-control" id="signature_file" name="signature_file"
                               accept=".png,.jpg,.jpeg,.gif,.bmp,.webp">
                        <div class="form-text">Upload your digital signature (PNG, JPG, JPEG, GIF, BMP, WEBP only)</div>
                        {% if profile and profile.signature_filename %}
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-check me-1"></i>Current signature: {{ profile.signature_filename }}
                            </small>
                            <div class="mt-2">
                                <img src="{{ url_for('uploaded_signature', filename=profile.signature_filename) }}"
                                     alt="Current Signature" class="img-thumbnail" style="max-height: 100px;">
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('supervisor_dashboard') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Account Information
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ current_user.name }}</p>
                <p><strong>Email:</strong> {{ current_user.email }}</p>
                <p><strong>Phone:</strong> {{ current_user.phone or 'Not provided' }}</p>
                <p><strong>Role:</strong> {{ current_user.role.name }}</p>
                <p><strong>Member Since:</strong> {{ current_user.created_at.strftime('%B %d, %Y') }}</p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Profile Guidelines
                </h5>
            </div>
            <div class="card-body">
                <h6>Required Information</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Institution name</li>
                    <li><i class="fas fa-check text-success me-2"></i>Department</li>
                </ul>

                <h6 class="mt-3">Matching Criteria</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-users text-primary me-2"></i>Students from same institution</li>
                    <li><i class="fas fa-users text-primary me-2"></i>Students from same department</li>
                </ul>

                <div class="alert alert-info mt-3">
                    <small>
                        <strong>Note:</strong> Students will only be assigned to you if they are from the same institution and department.
                    </small>
                </div>
            </div>
        </div>

        {% if profile %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Assignment Eligibility
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h6 class="text-success">Profile Complete</h6>
                    <p class="text-muted">You can now be assigned to students from:</p>
                    <div class="alert alert-success">
                        <strong>{{ profile.institution }}</strong><br>
                        <small>{{ profile.department }} Department</small>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Profile Status
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h6 class="text-warning">Profile Incomplete</h6>
                    <p class="text-muted">Complete your profile to be eligible for student assignments.</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
