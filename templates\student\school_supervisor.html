{% extends "base.html" %}

{% block title %}School Supervisor Details - Student Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_logs') }}">
                    <i class="fas fa-clipboard-list me-2"></i>My Logs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('new_log_entry') }}">
                    <i class="fas fa-plus me-2"></i>New Log Entry
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('view_school_supervisor') }}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>School Supervisor
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chalkboard-teacher me-2"></i>My School Supervisor
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('student_dashboard') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Supervisor Profile Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-tie me-2"></i>Supervisor Profile
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <!-- Profile Picture -->
                        {% if supervisor.profile_picture %}
                            <img src="{{ url_for('uploaded_profile', filename=supervisor.profile_picture) }}"
                                 alt="Supervisor Profile Picture"
                                 class="rounded-circle mb-3"
                                 style="width: 150px; height: 150px; object-fit: cover; border: 3px solid #dee2e6;">
                        {% else %}
                            <div class="rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center"
                                 style="width: 150px; height: 150px; background-color: #6c757d; color: white; font-size: 3rem; border: 3px solid #dee2e6;">
                                {{ supervisor.name[0].upper() }}
                            </div>
                        {% endif %}
                        
                        <!-- Contact Actions -->
                        <div class="d-grid gap-2">
                            <a href="mailto:{{ supervisor.email }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-envelope me-1"></i>Send Email
                            </a>
                            {% if supervisor.phone %}
                            <a href="tel:{{ supervisor.phone }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-phone me-1"></i>Call
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-8">
                        <!-- Basic Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Personal Information</h6>
                                <p><strong>Name:</strong> {{ supervisor.name }}</p>
                                <p><strong>Email:</strong> {{ supervisor.email }}</p>
                                <p><strong>Phone:</strong> {{ supervisor.phone or 'Not provided' }}</p>
                                {% if supervisor_profile and supervisor_profile.position %}
                                <p><strong>Position:</strong> {{ supervisor_profile.position }}</p>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-primary">Academic Information</h6>
                                {% if supervisor_profile %}
                                <p><strong>Institution:</strong> {{ supervisor_profile.institution }}</p>
                                <p><strong>Department:</strong> {{ supervisor_profile.department }}</p>
                                {% if supervisor_profile.faculty %}
                                <p><strong>Faculty:</strong> {{ supervisor_profile.faculty }}</p>
                                {% endif %}
                                {% if supervisor_profile.office_location %}
                                <p><strong>Office:</strong> {{ supervisor_profile.office_location }}</p>
                                {% endif %}
                                {% else %}
                                <p class="text-muted">Profile details not available</p>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Professional Information -->
                        {% if supervisor_profile and (supervisor_profile.specialization or supervisor_profile.years_of_experience) %}
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6 class="text-primary">Professional Background</h6>
                                {% if supervisor_profile.specialization %}
                                <p><strong>Specialization:</strong> {{ supervisor_profile.specialization }}</p>
                                {% endif %}
                                {% if supervisor_profile.years_of_experience %}
                                <p><strong>Experience:</strong> {{ supervisor_profile.years_of_experience }} years</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Communication Guidelines -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-comments me-2"></i>Communication Guidelines
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>When to Contact Your Supervisor</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Questions about log entries</li>
                            <li><i class="fas fa-check text-success me-2"></i>Clarification on feedback</li>
                            <li><i class="fas fa-check text-success me-2"></i>Academic guidance needed</li>
                            <li><i class="fas fa-check text-success me-2"></i>SIWES-related concerns</li>
                            <li><i class="fas fa-check text-success me-2"></i>Progress discussions</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Communication Best Practices</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Be clear and specific</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Use professional language</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Include relevant details</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Respect response time</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Follow up appropriately</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="mailto:{{ supervisor.email }}?subject=SIWES Inquiry - {{ student.user.name }}" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>Email Supervisor
                    </a>
                    <a href="{{ url_for('student_logs') }}" class="btn btn-outline-primary">
                        <i class="fas fa-clipboard-list me-2"></i>View My Logs
                    </a>
                    <a href="{{ url_for('new_log_entry') }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>New Log Entry
                    </a>
                </div>
            </div>
        </div>

        <!-- Supervision Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Your Progress
                </h6>
            </div>
            <div class="card-body">
                {% set student_logs = student.log_entries %}
                {% set total_logs = student_logs|length %}
                {% set approved_logs = student_logs|selectattr('status', 'equalto', 'Approved')|list|length %}
                {% set pending_logs = student_logs|selectattr('status', 'equalto', 'Pending')|list|length %}
                {% set rejected_logs = student_logs|selectattr('status', 'equalto', 'Rejected')|list|length %}
                
                <div class="text-center mb-3">
                    <h4 class="text-primary">{{ total_logs }}</h4>
                    <p class="text-muted">Total Log Entries</p>
                </div>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-success">{{ approved_logs }}</h6>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="text-warning">{{ pending_logs }}</h6>
                            <small class="text-muted">Pending</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="text-danger">{{ rejected_logs }}</h6>
                        <small class="text-muted">Rejected</small>
                    </div>
                </div>
                
                {% if total_logs > 0 %}
                <div class="mt-3">
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ (approved_logs / total_logs * 100)|round(1) }}%"
                             title="Approved: {{ (approved_logs / total_logs * 100)|round(1) }}%"></div>
                        <div class="progress-bar bg-warning" role="progressbar" 
                             style="width: {{ (pending_logs / total_logs * 100)|round(1) }}%"
                             title="Pending: {{ (pending_logs / total_logs * 100)|round(1) }}%"></div>
                        <div class="progress-bar bg-danger" role="progressbar" 
                             style="width: {{ (rejected_logs / total_logs * 100)|round(1) }}%"
                             title="Rejected: {{ (rejected_logs / total_logs * 100)|round(1) }}%"></div>
                    </div>
                    <small class="text-muted">
                        Approval Rate: {{ (approved_logs / total_logs * 100)|round(1) }}%
                    </small>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-address-card me-2"></i>Contact Information
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>Email:</strong><br>
                    <a href="mailto:{{ supervisor.email }}" class="text-decoration-none">
                        {{ supervisor.email }}
                    </a>
                </div>
                {% if supervisor.phone %}
                <div class="mb-2">
                    <strong>Phone:</strong><br>
                    <a href="tel:{{ supervisor.phone }}" class="text-decoration-none">
                        {{ supervisor.phone }}
                    </a>
                </div>
                {% endif %}
                {% if supervisor_profile and supervisor_profile.office_location %}
                <div class="mb-2">
                    <strong>Office:</strong><br>
                    {{ supervisor_profile.office_location }}
                </div>
                {% endif %}
                {% if supervisor_profile %}
                <div class="mb-2">
                    <strong>Department:</strong><br>
                    {{ supervisor_profile.department }}
                </div>
                <div>
                    <strong>Institution:</strong><br>
                    {{ supervisor_profile.institution }}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Help -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>Need Help?
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    If you're unable to reach your school supervisor or need additional support, 
                    contact the SIWES coordinator at your institution or the system administrator.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
