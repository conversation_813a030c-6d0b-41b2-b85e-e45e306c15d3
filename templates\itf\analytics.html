{% extends "base.html" %}

{% block title %}Analytics - ITF Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_students') }}">
                    <i class="fas fa-users me-2"></i>All Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('itf_analytics') }}">
                    <i class="fas fa-chart-pie me-2"></i>Analytics
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-pie me-2"></i>Analytics - {{ assigned_state }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync me-1"></i>Refresh
            </button>
        </div>
    </div>
</div>

<!-- State Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">State Students</h5>
                        <h2 class="mb-0">{{ total_state_students }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Log Entries</h5>
                        <h2 class="mb-0">{{ total_state_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Approved Logs</h5>
                        <h2 class="mb-0">{{ approved_state_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Fully Assigned</h5>
                        <h2 class="mb-0">{{ state_fully_assigned }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>Student Registration Trend
                </h5>
            </div>
            <div class="card-body">
                <canvas id="registrationChart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Log Submission Activity
                </h5>
            </div>
            <div class="card-body">
                <canvas id="submissionChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-university me-2"></i>Department Distribution
                </h5>
            </div>
            <div class="card-body">
                <canvas id="departmentChart" height="250"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-tie me-2"></i>Supervisor Assignment
                </h5>
            </div>
            <div class="card-body">
                <canvas id="supervisorChart" height="250"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Review Response Time
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <h3 class="text-primary">{{ "%.1f"|format(avg_review_time) }} days</h3>
                    <p class="text-muted">Average Review Time</p>
                    {% if avg_review_time <= 2 %}
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 100%"></div>
                        </div>
                        <small class="text-success">Excellent response time!</small>
                    {% elif avg_review_time <= 5 %}
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 100%"></div>
                        </div>
                        <small class="text-warning">Good response time</small>
                    {% else %}
                        <div class="progress">
                            <div class="progress-bar bg-danger" style="width: 100%"></div>
                        </div>
                        <small class="text-danger">Needs improvement</small>
                    {% endif %}
                    <br><br>
                    <small class="text-muted">
                        <span class="badge bg-success">≤2 days</span>
                        <span class="badge bg-warning">3-5 days</span>
                        <span class="badge bg-danger">>5 days</span>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-area me-2"></i>System Usage Over Time
                </h5>
            </div>
            <div class="card-body">
                <canvas id="usageChart" height="150"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>Top Performing Students
                </h5>
            </div>
            <div class="card-body">
                {% if top_students %}
                    <div class="list-group list-group-flush">
                        {% for student, total_logs, approved_logs in top_students %}
                            {% set approval_rate = (approved_logs / total_logs * 100) if total_logs > 0 else 0 %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    {% if student.user.profile_picture %}
                                        <img src="{{ url_for('uploaded_profile', filename=student.user.profile_picture) }}"
                                             alt="Profile" class="avatar-circle-small me-2">
                                    {% else %}
                                        <div class="avatar-circle-small me-2">
                                            {{ student.user.name[0].upper() }}
                                        </div>
                                    {% endif %}
                                    <div>
                                        <strong>{{ student.user.name }}</strong><br>
                                        <small class="text-muted">{{ student.department or 'Not specified' }}</small><br>
                                        <small class="text-info">{{ total_logs }} logs submitted</small>
                                    </div>
                                </div>
                                <span class="badge bg-{{ 'success' if approval_rate >= 90 else 'warning' if approval_rate >= 70 else 'danger' }}">
                                    {{ "%.0f"|format(approval_rate) }}%
                                </span>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>No students with sufficient log entries (minimum 5) found in {{ assigned_state }}.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>Key Performance Indicators
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-primary">{{ "%.1f"|format(participation_rate) }}%</h4>
                            <p class="text-muted mb-0">State Participation Rate</p>
                            <small class="text-muted">{{ total_state_students }} of all students</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-success">{{ "%.1f"|format(approval_rate) }}%</h4>
                            <p class="text-muted mb-0">Log Approval Rate</p>
                            <small class="text-muted">{{ approved_state_logs }} of {{ total_state_logs }} logs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <h4 class="text-info">{{ "%.1f"|format(assignment_rate) }}%</h4>
                            <p class="text-muted mb-0">Full Assignment Rate</p>
                            <small class="text-muted">{{ state_fully_assigned }} students fully assigned</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            {% if total_state_logs > 0 %}
                                {% set avg_logs_per_student = total_state_logs / total_state_students if total_state_students > 0 else 0 %}
                                <h4 class="text-warning">{{ "%.1f"|format(avg_logs_per_student) }}</h4>
                                <p class="text-muted mb-0">Avg Logs per Student</p>
                                <small class="text-muted">Activity level indicator</small>
                            {% else %}
                                <h4 class="text-warning">0</h4>
                                <p class="text-muted mb-0">Avg Logs per Student</p>
                                <small class="text-muted">No activity yet</small>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.avatar-circle-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
    object-fit: cover;
    border: 2px solid #dee2e6;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Real data from backend
{% if monthly_registrations %}
    // Prepare monthly data (reverse to show chronologically)
    const monthlyData = [
        {% for year, month, count in monthly_registrations|reverse %}
            {year: {{ year }}, month: {{ month }}, count: {{ count }}}{% if not loop.last %},{% endif %}
        {% endfor %}
    ];

    const months = monthlyData.map(item => {
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return monthNames[item.month - 1] + ' ' + item.year;
    });
    const registrationData = monthlyData.map(item => item.count);
{% else %}
    const months = ['No Data'];
    const registrationData = [0];
{% endif %}

{% if monthly_logs %}
    // Prepare monthly log data (reverse to show chronologically)
    const monthlyLogData = [
        {% for year, month, count in monthly_logs|reverse %}
            {year: {{ year }}, month: {{ month }}, count: {{ count }}}{% if not loop.last %},{% endif %}
        {% endfor %}
    ];
    const submissionData = monthlyLogData.map(item => item.count);
{% else %}
    const submissionData = [0];
{% endif %}

// Registration Trend Chart
const regCtx = document.getElementById('registrationChart').getContext('2d');
new Chart(regCtx, {
    type: 'line',
    data: {
        labels: months,
        datasets: [{
            label: 'New Registrations',
            data: registrationData,
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Submission Activity Chart
const subCtx = document.getElementById('submissionChart').getContext('2d');
new Chart(subCtx, {
    type: 'bar',
    data: {
        labels: months,
        datasets: [{
            label: 'Log Submissions',
            data: submissionData,
            backgroundColor: '#28a745'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Department Distribution Chart
const deptCtx = document.getElementById('departmentChart').getContext('2d');
{% if dept_stats %}
new Chart(deptCtx, {
    type: 'pie',
    data: {
        labels: [{% for dept, count in dept_stats %}'{{ dept or "Not Specified" }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for dept, count in dept_stats %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d', '#17a2b8', '#fd7e14', '#e83e8c']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% else %}
new Chart(deptCtx, {
    type: 'pie',
    data: {
        labels: ['No Data Available'],
        datasets: [{
            data: [1],
            backgroundColor: ['#dee2e6']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// Supervisor Assignment Chart
const supCtx = document.getElementById('supervisorChart').getContext('2d');
{% if total_state_students > 0 %}
    {% set industry_only = state_students_with_industry - state_fully_assigned %}
    {% set school_only = state_students_with_school - state_fully_assigned %}
    {% set none_assigned = total_state_students - state_students_with_industry - state_students_with_school + state_fully_assigned %}
new Chart(supCtx, {
    type: 'doughnut',
    data: {
        labels: ['Both Assigned', 'Industry Only', 'School Only', 'None'],
        datasets: [{
            data: [{{ state_fully_assigned }}, {{ industry_only }}, {{ school_only }}, {{ none_assigned }}],
            backgroundColor: ['#28a745', '#17a2b8', '#ffc107', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% else %}
new Chart(supCtx, {
    type: 'doughnut',
    data: {
        labels: ['No Students'],
        datasets: [{
            data: [1],
            backgroundColor: ['#dee2e6']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// Usage Chart - Combined Registration and Log Submission Trends
const usageCtx = document.getElementById('usageChart').getContext('2d');
new Chart(usageCtx, {
    type: 'line',
    data: {
        labels: months,
        datasets: [{
            label: 'Student Registrations',
            data: registrationData,
            borderColor: '#17a2b8',
            backgroundColor: 'rgba(23, 162, 184, 0.1)',
            tension: 0.4,
            fill: false
        }, {
            label: 'Log Submissions',
            data: submissionData,
            borderColor: '#ffc107',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.4,
            fill: false
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        plugins: {
            legend: {
                position: 'top'
            }
        }
    }
});
</script>
{% endblock %}
