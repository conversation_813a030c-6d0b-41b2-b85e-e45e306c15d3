{% extends "base.html" %}

{% block title %}Review Log Entry - {{ log.log_date.strftime('%Y-%m-%d') }} - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_students') }}">
                    <i class="fas fa-users me-2"></i>My Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_reviews') }}">
                    <i class="fas fa-clipboard-check me-2"></i>Pending Reviews
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-clipboard-check me-2"></i>Review Log Entry
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('supervisor_reviews') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Reviews
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar me-2"></i>{{ log.log_date.strftime('%A, %B %d, %Y') }}
                    </h5>
                    <span class="badge bg-warning">{{ log.status }}</span>
                </div>
            </div>
            <div class="card-body">
                <h6><i class="fas fa-tasks me-2"></i>Activities Performed</h6>
                <div class="bg-light p-3 rounded mb-4">
                    {{ log.activities_performed|replace('\n', '<br>')|safe }}
                </div>

                {% if log.key_learnings %}
                <h6><i class="fas fa-lightbulb me-2"></i>Key Learnings</h6>
                <div class="bg-light p-3 rounded mb-4">
                    {{ log.key_learnings|replace('\n', '<br>')|safe }}
                </div>
                {% endif %}

                <!-- Review Form -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-comment me-2"></i>{{ supervisor_type }} Review
                        </h6>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="comments" class="form-label">Review Comments</label>
                                <textarea class="form-control" id="comments" name="comments" rows="5"
                                          placeholder="Provide your feedback on this log entry..." required></textarea>
                                <div class="form-text">
                                    Provide constructive feedback to help the student improve their reporting and learning.
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="submit" name="action" value="reject" class="btn btn-warning me-md-2">
                                    <i class="fas fa-times me-2"></i>Request Revision
                                </button>
                                <button type="submit" name="action" value="approve" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i>Approve Entry
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-graduate me-2"></i>Student Information
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ log.student.user.name }}</p>
                <p><strong>Email:</strong> {{ log.student.user.email }}</p>
                <p><strong>Matric Number:</strong> {{ log.student.matric_number }}</p>
                <p><strong>Department:</strong> {{ log.student.department or 'Not specified' }}</p>
                <p><strong>Institution:</strong> {{ log.student.institution or 'Not specified' }}</p>
                <p><strong>Organization:</strong> {{ log.student.siwes_organization_name or 'Not specified' }}</p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Entry Details
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Date:</strong> {{ log.log_date.strftime('%Y-%m-%d') }}</p>
                <p><strong>Day:</strong> {{ log.log_date.strftime('%A') }}</p>
                <p><strong>Status:</strong> <span class="badge bg-warning">{{ log.status }}</span></p>
                <p><strong>Submitted:</strong> {{ log.created_at.strftime('%Y-%m-%d %H:%M') }}</p>

                <!-- Days since submission -->
                {% set days_old = days_since(log.log_date) %}
                {% if days_old > 0 %}
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-clock me-2"></i>
                        This entry is {{ days_old }} day{{ 's' if days_old != 1 else '' }} old.
                    </small>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Student Progress
                </h5>
            </div>
            <div class="card-body">
                {% set student_logs = log.student.log_entries %}
                {% set total_logs = student_logs|length %}
                {% set approved_logs = student_logs|selectattr('status', 'equalto', 'Approved')|list|length %}
                {% set pending_logs = student_logs|selectattr('status', 'equalto', 'Pending')|list|length %}

                <p><strong>Total Entries:</strong> {{ total_logs }}</p>
                <p><strong>Approved:</strong> {{ approved_logs }}</p>
                <p><strong>Pending:</strong> {{ pending_logs }}</p>

                {% if total_logs > 0 %}
                <div class="progress">
                    <div class="progress-bar bg-success" role="progressbar"
                         style="width: {{ (approved_logs / total_logs * 100)|round(1) }}%">
                        {{ (approved_logs / total_logs * 100)|round(1) }}%
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Review Tips
                </h5>
            </div>
            <div class="card-body">
                <h6>Approve if:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Activities are well described</li>
                    <li><i class="fas fa-check text-success me-2"></i>Learning outcomes are clear</li>
                    <li><i class="fas fa-check text-success me-2"></i>Professional language used</li>
                </ul>

                <h6 class="mt-3">Request revision if:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-times text-danger me-2"></i>Insufficient detail</li>
                    <li><i class="fas fa-times text-danger me-2"></i>Unclear descriptions</li>
                    <li><i class="fas fa-times text-danger me-2"></i>Missing learning reflection</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Character count for textarea
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById('comments');
    const maxLength = 500;

    // Create character count element
    const countElement = document.createElement('div');
    countElement.className = 'form-text text-end';
    countElement.innerHTML = `<small>0 / ${maxLength} characters</small>`;
    textarea.parentNode.appendChild(countElement);

    // Update character count
    textarea.addEventListener('input', function() {
        const currentLength = this.value.length;
        countElement.innerHTML = `<small>${currentLength} / ${maxLength} characters</small>`;

        if (currentLength > maxLength * 0.9) {
            countElement.className = 'form-text text-end text-warning';
        } else {
            countElement.className = 'form-text text-end';
        }
    });
});

// Confirmation for actions
document.addEventListener('DOMContentLoaded', function() {
    const approveBtn = document.querySelector('button[value="approve"]');
    const rejectBtn = document.querySelector('button[value="reject"]');

    approveBtn.addEventListener('click', function(e) {
        if (!confirm('Are you sure you want to approve this log entry?')) {
            e.preventDefault();
        }
    });

    rejectBtn.addEventListener('click', function(e) {
        if (!confirm('Are you sure you want to request revision for this log entry?')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
