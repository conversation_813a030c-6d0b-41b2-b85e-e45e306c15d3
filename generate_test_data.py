#!/usr/bin/env python3
"""
Test Data Generation Script for SIWES System
Creates random users and logbook entries for testing purposes.
"""

import sys
import os
import random
from datetime import datetime, timedelta
from faker import Faker
from werkzeug.security import generate_password_hash

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db, User, Role, Student, SchoolSupervisor, IndustrySupervisor, ITFSupervisor, LogEntry

# Initialize Faker for Nigerian data
fake = Faker('en_NG')

# --- Configuration ---
NUM_STUDENTS = 50
NUM_INDUSTRY_SUPERVISORS = 10
NUM_SCHOOL_SUPERVISORS = 15
NUM_ITF_SUPERVISORS = 7
MIN_LOGS_PER_STUDENT = 15
MAX_LOGS_PER_STUDENT = 25

SCHOOLS = [
    ("University of Lagos", ["Computer Science", "Electrical Engineering", "Mechanical Engineering"]),
    ("Ahmadu Bello University", ["Civil Engineering", "Chemical Engineering", "Architecture"]),
    ("University of Ibadan", ["Medicine", "Agriculture", "Geology"]),
    ("Obafemi Awolowo University", ["Law", "Pharmacy", "Computer Engineering"]),
    ("University of Nigeria, Nsukka", ["Economics", "Biochemistry", "Microbiology"]),
    ("Covenant University", ["Information Technology", "Accounting", "Mass Communication"])
]

STATES = ["Lagos", "Abuja", "Rivers", "Kano", "Enugu", "Oyo", "Kaduna"]

INDUSTRIES = [
    ("Tech", ["Software Development", "Network Administration", "Data Analysis"]),
    ("Engineering", ["Construction", "Power Systems", "Telecommunications"]),
    ("Health", ["Hospital", "Pharmaceuticals", "Medical Research"]),
    ("Finance", ["Banking", "Insurance", "Auditing"]),
    ("Media", ["Broadcasting", "Journalism", "Public Relations"])
]

LOG_ACTIVITIES = {
    "Tech": [
        "Developed a new feature for the company's web application.",
        "Assisted in debugging and fixing issues in the production environment.",
        "Participated in a code review session and provided feedback.",
        "Wrote unit tests for a critical module.",
        "Designed a database schema for a new project."
    ],
    "Engineering": [
        "Conducted a site inspection for a new construction project.",
        "Assisted in the design of a new electrical system.",
        "Performed quality control checks on materials.",
        "Prepared a bill of quantities for a project.",
        "Attended a project planning meeting with clients."
    ],
    "Health": [
        "Assisted in the diagnosis and treatment of patients.",
        "Conducted laboratory tests and analyzed results.",
        "Participated in a health outreach program.",
        "Shadowed a senior doctor during surgery.",
        "Prepared a research paper on a prevalent disease."
    ],
    "Finance": [
        "Assisted in the preparation of financial statements.",
        "Conducted an audit of a client's financial records.",
        "Analyzed market trends and prepared a report.",
        "Assisted in the development of a new financial product.",
        "Attended a meeting with investors."
    ],
    "Media": [
        "Wrote a news story for the company's website.",
        "Assisted in the production of a television program.",
        "Conducted an interview with a public figure.",
        "Managed the company's social media accounts.",
        "Attended a press conference."
    ]
}

def create_users():
    """Create all users and return a dictionary of login details."""
    login_details = {}
    with app.app_context():
        # Get roles
        student_role = Role.query.filter_by(name='Student').first()
        industry_role = Role.query.filter_by(name='Industry Supervisor').first()
        school_role = Role.query.filter_by(name='School Supervisor').first()
        itf_role = Role.query.filter_by(name='ITF Supervisor').first()

        # Create Students
        for i in range(NUM_STUDENTS):
            name = fake.name()
            email = f"student{i+1}@test.com"
            password = "password"
            user = User(
                name=name,
                email=email,
                phone=fake.phone_number(),
                password_hash=generate_password_hash(password),
                role_id=student_role.id
            )
            db.session.add(user)
            login_details[email] = password
        db.session.commit()

        # Create Industry Supervisors
        for i in range(NUM_INDUSTRY_SUPERVISORS):
            name = fake.name()
            email = f"industry{i+1}@test.com"
            password = "password"
            user = User(
                name=name,
                email=email,
                phone=fake.phone_number(),
                password_hash=generate_password_hash(password),
                role_id=industry_role.id
            )
            db.session.add(user)
            login_details[email] = password
        db.session.commit()

        # Create School Supervisors
        for i in range(NUM_SCHOOL_SUPERVISORS):
            name = fake.name()
            email = f"school{i+1}@test.com"
            password = "password"
            user = User(
                name=name,
                email=email,
                phone=fake.phone_number(),
                password_hash=generate_password_hash(password),
                role_id=school_role.id
            )
            db.session.add(user)
            login_details[email] = password
        db.session.commit()

        # Create ITF Supervisors
        for i in range(NUM_ITF_SUPERVISORS):
            name = fake.name()
            email = f"itf{i+1}@test.com"
            password = "password"
            user = User(
                name=name,
                email=email,
                phone=fake.phone_number(),
                password_hash=generate_password_hash(password),
                role_id=itf_role.id
            )
            db.session.add(user)
            login_details[email] = password
        db.session.commit()

    return login_details

def create_profiles():
    """Create profiles for all users."""
    with app.app_context():
        # Student Profiles
        students = User.query.filter_by(role_id=Role.query.filter_by(name='Student').first().id).all()
        for student_user in students:
            school_name, departments = random.choice(SCHOOLS)
            department = random.choice(departments)
            industry_name, _ = random.choice(INDUSTRIES)
            student = Student(
                user_id=student_user.id,
                matric_number=f"TEST/{random.randint(1000, 9999)}",
                department=department,
                institution=school_name,
                siwes_organization_name=f"{industry_name} Corp",
                siwes_organization_address=fake.address(),
                siwes_state=random.choice(STATES),
                siwes_start_date=datetime.now() - timedelta(days=random.randint(30, 60)),
                siwes_end_date=datetime.now() + timedelta(days=random.randint(90, 120))
            )
            db.session.add(student)
        db.session.commit()

        # Industry Supervisor Profiles
        industry_supervisors = User.query.filter_by(role_id=Role.query.filter_by(name='Industry Supervisor').first().id).all()
        for industry_user in industry_supervisors:
            industry_name, departments = random.choice(INDUSTRIES)
            profile = IndustrySupervisor(
                user_id=industry_user.id,
                company_name=f"{industry_name} Solutions",
                industry_type=industry_name,
                position=random.choice(["Manager", "Senior Engineer", "Team Lead"]),
                department=random.choice(departments),
                company_address=fake.address()
            )
            db.session.add(profile)
        db.session.commit()

        # School Supervisor Profiles
        school_supervisors = User.query.filter_by(role_id=Role.query.filter_by(name='School Supervisor').first().id).all()
        for school_user in school_supervisors:
            school_name, departments = random.choice(SCHOOLS)
            profile = SchoolSupervisor(
                user_id=school_user.id,
                institution=school_name,
                department=random.choice(departments),
                position=random.choice(["Lecturer I", "Senior Lecturer", "Professor"])
            )
            db.session.add(profile)
        db.session.commit()

        # ITF Supervisor Profiles
        itf_supervisors = User.query.filter_by(role_id=Role.query.filter_by(name='ITF Supervisor').first().id).all()
        for i, itf_user in enumerate(itf_supervisors):
            profile = ITFSupervisor(
                user_id=itf_user.id,
                assigned_state=STATES[i % len(STATES)]
            )
            db.session.add(profile)
        db.session.commit()

def create_log_entries():
    """Create random log entries for all students."""
    with app.app_context():
        students = Student.query.all()
        for student in students:
            num_logs = random.randint(MIN_LOGS_PER_STUDENT, MAX_LOGS_PER_STUDENT)
            for i in range(num_logs):
                log_date = student.siwes_start_date + timedelta(days=i)
                industry_name = student.siwes_organization_name.split(" ")[0]
                activities = random.choice(LOG_ACTIVITIES.get(industry_name, LOG_ACTIVITIES["Tech"]))
                log = LogEntry(
                    student_id=student.id,
                    log_date=log_date,
                    activities_performed=activities,
                    key_learnings=fake.sentence(),
                    status=random.choice(["Pending", "Approved", "Rejected"])
                )
                db.session.add(log)
        db.session.commit()

def save_login_details(login_details):
    """Save login details to a markdown file."""
    with open("test_user_logins.md", "w") as f:
        f.write("# Test User Login Details\n\n")
        f.write("| Email | Password |\n")
        f.write("|---|---|\n")
        for email, password in login_details.items():
            f.write(f"| {email} | {password} |\n")

if __name__ == '__main__':
    print("Generating test data...")
    login_details = create_users()
    create_profiles()
    create_log_entries()
    save_login_details(login_details)
    print("Test data generation complete!")
    print("Login details saved to test_user_logins.md")
