{% extends "base.html" %}

{% block title %}All Students - ITF Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('itf_students') }}">
                    <i class="fas fa-users me-2"></i>All Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_analytics') }}">
                    <i class="fas fa-chart-pie me-2"></i>Analytics
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>Students in {{ assigned_state if assigned_state else 'All States' }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <span class="badge bg-primary">{{ students.total }} Total Students</span>
        </div>
    </div>
</div>

{% if students.items %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Student</th>
                        <th>Department</th>
                        <th>Organization</th>
                        <th>State</th>
                        <th>Supervisors</th>
                        <th>Progress</th>
                        <th>Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students.items %}
                    <tr>
                        <td>
                            <strong>{{ student.user.name }}</strong><br>
                            <small class="text-muted">{{ student.matric_number }}</small><br>
                            <small class="text-muted">{{ student.user.email }}</small>
                        </td>
                        <td>
                            {{ student.department or 'Not specified' }}<br>
                            <small class="text-muted">{{ student.institution or 'Not specified' }}</small>
                        </td>
                        <td>
                            {{ student.siwes_organization_name or 'Not specified' }}
                        </td>
                        <td>
                            {% if student.siwes_state %}
                                <span class="badge bg-primary">{{ student.siwes_state }}</span>
                            {% else %}
                                <span class="badge bg-secondary">Not specified</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if student.industry_supervisor %}
                                <span class="badge bg-info">Industry: {{ student.industry_supervisor.name }}</span><br>
                            {% endif %}
                            {% if student.school_supervisor %}
                                <span class="badge bg-success">School: {{ student.school_supervisor.name }}</span>
                            {% endif %}
                            {% if not student.industry_supervisor and not student.school_supervisor %}
                                <span class="badge bg-warning">No supervisors</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set student_logs = student.log_entries %}
                            {% set total_logs = student_logs|length %}
                            {% set approved_logs = student_logs|selectattr('status', 'equalto', 'Approved')|list|length %}
                            {% set pending_logs = student_logs|selectattr('status', 'equalto', 'Pending')|list|length %}

                            <div class="text-center">
                                <small class="text-muted">{{ total_logs }} total</small><br>
                                <span class="badge bg-success">{{ approved_logs }}</span>
                                <span class="badge bg-warning">{{ pending_logs }}</span>
                            </div>
                        </td>
                        <td>
                            <small class="text-muted">{{ student.created_at.strftime('%Y-%m-%d') }}</small>
                        </td>
                        <td>
                            <a href="{{ url_for('itf_view_student', student_id=student.id) }}"
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>View Profile
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if students.pages > 1 %}
<nav aria-label="Students pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if students.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('itf_students', page=students.prev_num) }}">Previous</a>
            </li>
        {% endif %}

        {% for page_num in students.iter_pages() %}
            {% if page_num %}
                {% if page_num != students.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('itf_students', page=page_num) }}">{{ page_num }}</a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}

        {% if students.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('itf_students', page=students.next_num) }}">Next</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-users fa-4x text-muted mb-4"></i>
        <h4 class="text-muted">No Students Registered</h4>
        <p class="text-muted mb-4">
            No students have registered in the SIWES system yet.
        </p>
    </div>
</div>
{% endif %}

<!-- Summary Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x text-primary mb-3"></i>
                <h5>{{ students.total }}</h5>
                <p class="text-muted">Total Students</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-user-check fa-2x text-success mb-3"></i>
                {% set supervised_count = count_students_with_supervisors(students.items) %}
                <h5>{{ supervised_count }}</h5>
                <p class="text-muted">With Supervisors</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x text-info mb-3"></i>
                {% set with_org = students.items|selectattr('siwes_organization_name')|list|length %}
                <h5>{{ with_org }}</h5>
                <p class="text-muted">With Organizations</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x text-warning mb-3"></i>
                {% set active_count = students.items|selectattr('siwes_start_date')|list|length %}
                <h5>{{ active_count }}</h5>
                <p class="text-muted">Active SIWES</p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>ITF Oversight Guidelines
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Monitoring Responsibilities</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>System-wide oversight</li>
                            <li><i class="fas fa-check text-success me-2"></i>Quality assurance</li>
                            <li><i class="fas fa-check text-success me-2"></i>Progress monitoring</li>
                            <li><i class="fas fa-check text-success me-2"></i>Report generation</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Key Metrics</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Student participation rates</li>
                            <li><i class="fas fa-check text-success me-2"></i>Supervisor engagement</li>
                            <li><i class="fas fa-check text-success me-2"></i>Log entry quality</li>
                            <li><i class="fas fa-check text-success me-2"></i>System utilization</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
