{% extends "base.html" %}

{% block title %}Student Profile - ITF Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('itf_students') }}">
                    <i class="fas fa-users me-2"></i>All Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_analytics') }}">
                    <i class="fas fa-chart-pie me-2"></i>Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>Student Profile: {{ student.user.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('itf_students') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to Students
            </a>
            <a href="{{ url_for('itf_export_student_logbook', student_id=student.id) }}"
               class="btn btn-sm btn-primary" target="_blank">
                <i class="fas fa-download me-1"></i>Export Logbook
            </a>
        </div>
    </div>
</div>

<!-- Student Information -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>Student Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> {{ student.user.name }}</p>
                        <p><strong>Email:</strong> {{ student.user.email }}</p>
                        <p><strong>Phone:</strong> {{ student.user.phone or 'Not provided' }}</p>
                        <p><strong>Matric Number:</strong> {{ student.matric_number }}</p>
                        <p><strong>Department:</strong> {{ student.department or 'Not specified' }}</p>
                        <p><strong>Institution:</strong> {{ student.institution or 'Not specified' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>SIWES Organization:</strong> {{ student.siwes_organization_name or 'Not specified' }}</p>
                        <p><strong>Organization Address:</strong> {{ student.siwes_organization_address or 'Not specified' }}</p>
                        <p><strong>SIWES State:</strong> 
                            <span class="badge bg-primary">{{ student.siwes_state or 'Not specified' }}</span>
                        </p>
                        {% if student.siwes_start_date %}
                        <p><strong>SIWES Period:</strong><br>
                        {{ student.siwes_start_date.strftime('%Y-%m-%d') }} to 
                        {{ student.siwes_end_date.strftime('%Y-%m-%d') if student.siwes_end_date else 'Not set' }}</p>
                        {% endif %}
                        <p><strong>Registration Date:</strong> {{ student.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supervisors Information -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Assigned Supervisors
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Industry Supervisor</h6>
                        {% if student.industry_supervisor %}
                            <p><strong>Name:</strong> {{ student.industry_supervisor.name }}</p>
                            <p><strong>Email:</strong> {{ student.industry_supervisor.email }}</p>
                            <p><strong>Phone:</strong> {{ student.industry_supervisor.phone or 'Not provided' }}</p>
                        {% else %}
                            <p class="text-muted">No industry supervisor assigned</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <h6>School Supervisor</h6>
                        {% if student.school_supervisor %}
                            <p><strong>Name:</strong> {{ student.school_supervisor.name }}</p>
                            <p><strong>Email:</strong> {{ student.school_supervisor.email }}</p>
                            <p><strong>Phone:</strong> {{ student.school_supervisor.phone or 'Not provided' }}</p>
                        {% else %}
                            <p class="text-muted">No school supervisor assigned</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Log Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h3 class="text-primary">{{ total_logs }}</h3>
                    <p class="text-muted">Total Log Entries</p>
                </div>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success">{{ approved_logs }}</h5>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-warning">{{ pending_logs }}</h5>
                            <small class="text-muted">Pending</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-danger">{{ rejected_logs }}</h5>
                        <small class="text-muted">Rejected</small>
                    </div>
                </div>

                {% if total_logs > 0 %}
                <div class="mt-3">
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ (approved_logs / total_logs * 100)|round(1) }}%">
                            {{ (approved_logs / total_logs * 100)|round(1) }}%
                        </div>
                        <div class="progress-bar bg-warning" role="progressbar" 
                             style="width: {{ (pending_logs / total_logs * 100)|round(1) }}%">
                            {{ (pending_logs / total_logs * 100)|round(1) }}%
                        </div>
                        <div class="progress-bar bg-danger" role="progressbar" 
                             style="width: {{ (rejected_logs / total_logs * 100)|round(1) }}%">
                            {{ (rejected_logs / total_logs * 100)|round(1) }}%
                        </div>
                    </div>
                    <small class="text-muted">Approval Rate: {{ (approved_logs / total_logs * 100)|round(1) }}%</small>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Profile Picture -->
        {% if student.user.profile_picture %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-image me-2"></i>Profile Picture
                </h5>
            </div>
            <div class="card-body text-center">
                <img src="{{ url_for('uploaded_profile', filename=student.user.profile_picture) }}" 
                     alt="Profile Picture" class="img-fluid rounded" style="max-height: 200px;">
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Log Entries -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>Recent Log Entries
        </h5>
    </div>
    <div class="card-body">
        {% if logs.items %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Activities</th>
                        <th>Status</th>
                        <th>Reviews</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs.items %}
                    <tr>
                        <td>{{ log.log_date.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                                {{ log.activities_performed[:100] }}{% if log.activities_performed|length > 100 %}...{% endif %}
                            </div>
                        </td>
                        <td>
                            {% if log.status == 'Approved' %}
                                <span class="badge bg-success">{{ log.status }}</span>
                            {% elif log.status == 'Pending' %}
                                <span class="badge bg-warning">{{ log.status }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ log.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.industry_review_comments %}
                                <small class="text-success">Industry: Reviewed</small><br>
                            {% endif %}
                            {% if log.school_review_comments %}
                                <small class="text-info">School: Reviewed</small>
                            {% endif %}
                            {% if not log.industry_review_comments and not log.school_review_comments %}
                                <small class="text-muted">No reviews</small>
                            {% endif %}
                        </td>
                        <td>{{ log.created_at.strftime('%Y-%m-%d') }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if logs.pages > 1 %}
        <nav aria-label="Log entries pagination" class="mt-3">
            <ul class="pagination justify-content-center">
                {% if logs.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('itf_view_student', student_id=student.id, page=logs.prev_num) }}">Previous</a>
                    </li>
                {% endif %}

                {% for page_num in logs.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != logs.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('itf_view_student', student_id=student.id, page=page_num) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if logs.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('itf_view_student', student_id=student.id, page=logs.next_num) }}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-clipboard fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No Log Entries</h5>
            <p class="text-muted">This student hasn't submitted any log entries yet.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- ITF Final Signature Section -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-certificate me-2"></i>ITF Final Signature
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle me-2"></i>ITF Signature Requirements</h6>
            <ul class="mb-0">
                <li>Student must have completed at least 60% of logbook entries</li>
                <li>ITF supervisor signs at the end of SIWES program before logbook export</li>
                <li>Only one ITF supervisor per state can sign each student's logbook</li>
            </ul>
        </div>

        <!-- Logbook Completion Status -->
        {% set total_expected_days = ((student.siwes_end_date - student.siwes_start_date).days + 1) if student.siwes_start_date and student.siwes_end_date else 1 %}
        {% set completion_percentage = (logs.total / total_expected_days * 100) if total_expected_days > 0 else 0 %}

        <div class="row mb-3">
            <div class="col-md-6">
                <h6>Logbook Completion Status</h6>
                <div class="progress mb-2">
                    <div class="progress-bar {% if completion_percentage >= 60 %}bg-success{% else %}bg-warning{% endif %}"
                         role="progressbar" style="width: {{ completion_percentage }}%"
                         aria-valuenow="{{ completion_percentage }}" aria-valuemin="0" aria-valuemax="100">
                        {{ "%.1f"|format(completion_percentage) }}%
                    </div>
                </div>
                <small class="text-muted">
                    {{ logs.total }} entries out of {{ total_expected_days }} expected days
                </small>
            </div>
        </div>

        {% if completion_percentage >= 60 %}
            <form method="POST" action="{{ url_for('sign_final', student_id=student.id) }}" class="mt-3">
                <div class="mb-3">
                    <label for="comments" class="form-label">Final Comments</label>
                    <textarea class="form-control" id="comments" name="comments" rows="4"
                              placeholder="Add final comments about the student's overall SIWES performance..."></textarea>
                </div>
                <button type="submit" class="btn btn-success btn-lg">
                    <i class="fas fa-certificate me-2"></i>Provide Final ITF Signature
                </button>
            </form>
        {% else %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Student has not met the minimum 60% logbook completion requirement for final signature.
                Current completion: {{ "%.1f"|format(completion_percentage) }}%
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
