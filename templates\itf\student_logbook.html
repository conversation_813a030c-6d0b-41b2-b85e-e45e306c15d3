<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIWES Logbook - {{ student.user.name }}</title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
            .page-break { page-break-before: always; }
        }
        
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.4;
            margin: 20px;
            color: #000;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .header h2 {
            margin: 5px 0;
            font-size: 18px;
            font-weight: normal;
        }
        
        .student-info {
            display: grid;
            grid-template-columns: 1fr 150px;
            gap: 20px;
            margin-bottom: 30px;
            border: 1px solid #000;
            padding: 15px;
        }
        
        .student-details h3 {
            margin-top: 0;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: bold;
            width: 150px;
            flex-shrink: 0;
        }
        
        .info-value {
            flex: 1;
        }
        
        .photo-section {
            text-align: center;
            border: 1px solid #000;
            padding: 10px;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .photo-section img {
            max-width: 120px;
            max-height: 130px;
            border: 1px solid #ccc;
        }
        
        .photo-placeholder {
            color: #666;
            font-style: italic;
        }
        
        .logs-section {
            margin-top: 30px;
        }
        
        .logs-section h3 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 18px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        
        .week-entry {
            border: 1px solid #000;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }
        
        .week-header {
            background-color: #f0f0f0;
            padding: 10px;
            border-bottom: 1px solid #000;
            font-weight: bold;
        }
        
        .week-content {
            padding: 15px;
        }
        
        .log-date {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .log-activities {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        .log-learnings {
            margin-bottom: 15px;
            text-align: justify;
            font-style: italic;
        }
        
        .log-status {
            text-align: right;
            font-size: 12px;
            color: #666;
        }
        
        .status-approved { color: #28a745; }
        .status-pending { color: #ffc107; }
        .status-rejected { color: #dc3545; }
        
        .signatures-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        
        .signature-box {
            border: 1px solid #000;
            padding: 20px;
            height: 80px;
        }
        
        .signature-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .signature-line {
            border-bottom: 1px solid #000;
            height: 40px;
            margin-top: 20px;
        }
        
        .export-info {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">🖨️ Print Logbook</button>
    
    <div class="header">
        <h1>STUDENTS INDUSTRIAL WORK EXPERIENCE SCHEME (SIWES)</h1>
        <h2>STUDENT LOGBOOK</h2>
        <p>Industrial Training Fund (ITF) - Nigeria</p>
    </div>
    
    <div class="student-info">
        <div class="student-details">
            <h3>STUDENT INFORMATION</h3>
            
            <div class="info-row">
                <span class="info-label">Full Name:</span>
                <span class="info-value">{{ student.user.name }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Matric Number:</span>
                <span class="info-value">{{ student.matric_number }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Institution:</span>
                <span class="info-value">{{ student.institution }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Department:</span>
                <span class="info-value">{{ student.department }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Email:</span>
                <span class="info-value">{{ student.user.email }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Phone:</span>
                <span class="info-value">{{ student.user.phone or 'Not provided' }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">SIWES Organization:</span>
                <span class="info-value">{{ student.siwes_organization_name or 'Not specified' }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">Organization Address:</span>
                <span class="info-value">{{ student.siwes_organization_address or 'Not specified' }}</span>
            </div>
            
            <div class="info-row">
                <span class="info-label">SIWES State:</span>
                <span class="info-value">{{ student.siwes_state or 'Not specified' }}</span>
            </div>
            
            {% if student.siwes_start_date %}
            <div class="info-row">
                <span class="info-label">SIWES Period:</span>
                <span class="info-value">
                    {{ student.siwes_start_date.strftime('%d/%m/%Y') }} - 
                    {{ student.siwes_end_date.strftime('%d/%m/%Y') if student.siwes_end_date else 'Ongoing' }}
                </span>
            </div>
            {% endif %}
        </div>
        
        <div class="photo-section">
            {% if student.user.profile_picture %}
                <img src="{{ url_for('uploaded_profile', filename=student.user.profile_picture) }}" 
                     alt="Student Photo">
            {% else %}
                <div class="photo-placeholder">
                    STUDENT<br>PHOTOGRAPH
                </div>
            {% endif %}
        </div>
    </div>
    
    <div class="logs-section">
        <h3>WEEKLY ACTIVITY LOG ENTRIES</h3>
        
        {% if logs %}
            {% set current_week = 1 %}
            {% for log in logs %}
                {% if loop.index0 % 7 == 0 %}
                    {% if loop.index0 > 0 %}
                        </div></div> <!-- Close previous week -->
                    {% endif %}
                    <div class="week-entry">
                        <div class="week-header">
                            WEEK {{ current_week }} - {{ log.log_date.strftime('%B %Y') }}
                        </div>
                        <div class="week-content">
                    {% set current_week = current_week + 1 %}
                {% endif %}
                
                <div class="log-entry" style="margin-bottom: 20px; border-bottom: 1px dotted #ccc; padding-bottom: 15px;">
                    <div class="log-date">
                        📅 {{ log.log_date.strftime('%A, %B %d, %Y') }}
                    </div>
                    
                    <div class="log-activities">
                        <strong>Activities Performed:</strong><br>
                        {{ log.activities_performed }}
                    </div>
                    
                    {% if log.key_learnings %}
                    <div class="log-learnings">
                        <strong>Key Learnings:</strong><br>
                        {{ log.key_learnings }}
                    </div>
                    {% endif %}
                    
                    <div class="log-status">
                        Status: 
                        <span class="status-{{ log.status.lower() }}">
                            {{ log.status }}
                        </span>
                        {% if log.status == 'Approved' %}
                            {% if log.industry_review_date %}
                                | Industry Review: {{ log.industry_review_date.strftime('%d/%m/%Y') }}
                            {% endif %}
                            {% if log.school_review_date %}
                                | School Review: {{ log.school_review_date.strftime('%d/%m/%Y') }}
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
                
                {% if loop.last %}
                    </div></div> <!-- Close last week -->
                {% endif %}
            {% endfor %}
        {% else %}
            <div style="text-align: center; padding: 40px; color: #666;">
                <p>No log entries found for this student.</p>
            </div>
        {% endif %}
    </div>
    
    <div class="signatures-section">
        <div class="signature-box">
            <div class="signature-title">Industry Supervisor</div>
            <div>Name: {{ student.industry_supervisor.name if student.industry_supervisor else '________________________' }}</div>
            <div class="signature-line"></div>
            <div style="text-align: center; margin-top: 5px; font-size: 12px;">Signature & Date</div>
        </div>
        
        <div class="signature-box">
            <div class="signature-title">School Supervisor</div>
            <div>Name: {{ student.school_supervisor.name if student.school_supervisor else '________________________' }}</div>
            <div class="signature-line"></div>
            <div style="text-align: center; margin-top: 5px; font-size: 12px;">Signature & Date</div>
        </div>
    </div>
    
    <div class="export-info">
        <p>This logbook was exported on {{ export_date.strftime('%B %d, %Y at %I:%M %p') }} from the SIWES Reporting System</p>
        <p>Industrial Training Fund (ITF) - Nigeria | SIWES Digital Platform</p>
    </div>
</body>
</html>
