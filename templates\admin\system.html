{% extends "base.html" %}

{% block title %}System Settings - Admin Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users me-2"></i>Manage Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('assign_supervisor') }}">
                    <i class="fas fa-user-tie me-2"></i>Assign Supervisors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('admin_system') }}">
                    <i class="fas fa-cog me-2"></i>System Settings
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>System Settings
    </h1>
</div>

<!-- System Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Users</h5>
                        <h2 class="mb-0">{{ total_users }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Students</h5>
                        <h2 class="mb-0">{{ total_students }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-graduate fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Log Entries</h5>
                        <h2 class="mb-0">{{ total_logs }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning stats-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">System Health</h5>
                        <h2 class="mb-0">98%</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-heartbeat fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>System Configuration
                </h5>
            </div>
            <div class="card-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="system_name" class="form-label">System Name</label>
                            <input type="text" class="form-control" id="system_name" value="SIWES Reporting System">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="admin_email" class="form-label">Admin Email</label>
                            <input type="email" class="form-control" id="admin_email" value="<EMAIL>">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_log_length" class="form-label">Max Log Entry Length</label>
                            <input type="number" class="form-control" id="max_log_length" value="1000">
                            <div class="form-text">Maximum characters allowed in log entries</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="review_deadline" class="form-label">Review Deadline (days)</label>
                            <input type="number" class="form-control" id="review_deadline" value="7">
                            <div class="form-text">Days supervisors have to review log entries</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                            <label class="form-check-label" for="email_notifications">
                                Enable Email Notifications
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto_backup" checked>
                            <label class="form-check-label" for="auto_backup">
                                Enable Automatic Backups
                            </label>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-secondary me-md-2" onclick="resetSettings()">
                            <i class="fas fa-undo me-2"></i>Reset
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save me-2"></i>Save Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>Database Management
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-download fa-2x text-primary mb-2"></i>
                                <h6>Backup Database</h6>
                                <button class="btn btn-sm btn-primary" onclick="backupDatabase()">
                                    <i class="fas fa-download me-1"></i>Backup Now
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-upload fa-2x text-success mb-2"></i>
                                <h6>Restore Database</h6>
                                <button class="btn btn-sm btn-success" onclick="restoreDatabase()">
                                    <i class="fas fa-upload me-1"></i>Restore
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-broom fa-2x text-warning mb-2"></i>
                                <h6>Clean Database</h6>
                                <button class="btn btn-sm btn-warning" onclick="cleanDatabase()">
                                    <i class="fas fa-broom me-1"></i>Clean
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>System Activity
                </h5>
            </div>
            <div class="card-body">
                <h6>Recent Activity</h6>
                {% if recent_users %}
                    {% for user in recent_users %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <small><strong>{{ user.name }}</strong></small><br>
                            <small class="text-muted">{{ user.role.name }} registered</small>
                        </div>
                        <small class="text-muted">{{ user.created_at.strftime('%m-%d') }}</small>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No recent activity.</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>System Alerts
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <small><i class="fas fa-check-circle me-2"></i>System running normally</small>
                </div>
                <div class="alert alert-info">
                    <small><i class="fas fa-info-circle me-2"></i>Last backup: 2 hours ago</small>
                </div>
                <div class="alert alert-warning">
                    <small><i class="fas fa-exclamation-triangle me-2"></i>5 pending log reviews</small>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>System Tools
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_reports') }}" class="btn btn-outline-primary">
                        <i class="fas fa-chart-bar me-2"></i>View System Reports
                    </a>
                    <button class="btn btn-outline-secondary" onclick="clearCache()">
                        <i class="fas fa-trash me-2"></i>Clear Cache
                    </button>
                    <button class="btn btn-outline-info" onclick="systemCheck()">
                        <i class="fas fa-stethoscope me-2"></i>System Check
                    </button>
                    <button class="btn btn-outline-warning" onclick="maintenanceMode()">
                        <i class="fas fa-wrench me-2"></i>Maintenance Mode
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function saveSettings() {
    alert('Settings saved successfully!\n(Feature implementation coming soon)');
}

function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default?')) {
        alert('Settings reset to default values!');
    }
}

function backupDatabase() {
    if (confirm('Create a backup of the current database?')) {
        alert('Database backup created successfully!\n(Feature implementation coming soon)');
    }
}

function restoreDatabase() {
    if (confirm('This will restore the database from a backup. Continue?')) {
        alert('Database restore initiated!\n(Feature implementation coming soon)');
    }
}

function cleanDatabase() {
    if (confirm('This will clean up old data. Continue?')) {
        alert('Database cleanup completed!\n(Feature implementation coming soon)');
    }
}



function clearCache() {
    alert('System cache cleared!\n(Feature implementation coming soon)');
}

function systemCheck() {
    alert('Running system health check...\n✅ Database: OK\n✅ File System: OK\n✅ Memory: OK\n✅ Network: OK');
}

function maintenanceMode() {
    if (confirm('Enable maintenance mode? This will prevent users from accessing the system.')) {
        alert('Maintenance mode enabled!\n(Feature implementation coming soon)');
    }
}
</script>
{% endblock %}
