# 🎓 SIWES Reporting System

A comprehensive web-based Student Industrial Work Experience Scheme (SIWES) reporting and management system built with Flask.

## 🚀 **Quick Start**

### **1. Setup Environment**
```bash
# Activate virtual environment
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# Start application
python app.py
```

### **2. Access Application**
- **URL**: http://localhost:5000
- **Admin**: `<EMAIL>` / `admin123`
- **Student**: `<EMAIL>` / `student123`
- **School Supervisor**: `<EMAIL>` / `school123`

## ✨ **Features**

### **👨‍💼 Admin Management**
- ✅ User account management
- ✅ Supervisor assignment
- ✅ System reports and analytics
- ✅ Bulk user operations

### **👨‍🎓 Student Portal**
- ✅ Daily log entry creation
- ✅ Media file uploads (images, videos, documents)
- ✅ Log editing and resubmission
- ✅ School supervisor contact details
- ✅ Progress tracking

### **👨‍🏫 School Supervisor**
- ✅ Student log review and approval
- ✅ Monthly performance assessments
- ✅ Digital signature management
- ✅ Student progress monitoring

### **👨‍💼 Industry Supervisor**
- ✅ Student log review and feedback
- ✅ Industry-specific assessments
- ✅ Digital signature support
- ✅ Performance tracking

### **🏛️ ITF Supervisor**
- ✅ State-wide student monitoring
- ✅ Comprehensive reporting
- ✅ Export functionality
- ✅ Multi-state oversight

## 📁 **Project Structure**

```
SIWES/
├── app.py                          # Main application
├── templates/                      # HTML templates
│   ├── admin/                     # Admin interface
│   ├── student/                   # Student portal
│   ├── supervisor/                # Supervisor dashboard
│   └── itf/                       # ITF interface
├── uploads/                       # User files
├── venv/                          # Virtual environment
└── docs/                          # Documentation
```

## 🛠️ **Setup & Installation**

### **Prerequisites**
- Python 3.8+
- Flask and dependencies (included in venv)
- SQLite database (auto-created)

### **First Time Setup**
```bash
# 1. Activate virtual environment
venv\Scripts\activate

# 2. Create admin user (if needed)
python create_admin_user.py

# 3. Populate test data (optional)
python populate_test_data.py

# 4. Start application
python app.py
```

## 📚 **Documentation**

- **[Quick Test Accounts](QUICK_TEST_ACCOUNTS.md)** - Login credentials for testing
- **[User Management](COMPREHENSIVE_USER_ACCOUNTS.md)** - Complete user account guide
- **[Log Editing Feature](EDIT_AND_MONTHLY_FEATURES.md)** - Log editing and monthly comments
- **[Supervisor View](SCHOOL_SUPERVISOR_VIEW_FEATURE.md)** - School supervisor details
- **[Signatures & ITF](SIGNATURE_AND_ITF_FEATURES.md)** - Digital signatures and ITF features
- **[Project Structure](PROJECT_STRUCTURE.md)** - Detailed file organization

## 🔧 **Key Technologies**

- **Backend**: Flask (Python)
- **Database**: SQLite with SQLAlchemy ORM
- **Frontend**: Bootstrap 5, HTML5, CSS3, JavaScript
- **Authentication**: Flask-Login
- **File Upload**: Werkzeug secure file handling
- **Security**: Password hashing, role-based access control

## 🎯 **User Roles**

| Role | Description | Key Features |
|------|-------------|--------------|
| **Admin** | System administrator | User management, reports, assignments |
| **Student** | SIWES participants | Log entries, file uploads, progress tracking |
| **School Supervisor** | Academic supervisors | Log review, monthly assessments, signatures |
| **Industry Supervisor** | Workplace supervisors | Log review, industry feedback, signatures |
| **ITF Supervisor** | ITF officers | State monitoring, comprehensive reports |

## 📊 **System Capabilities**

### **Log Management**
- Daily activity logging
- Media file attachments
- Review and approval workflow
- Edit and resubmission for rejected logs
- Progress tracking and statistics

### **Assessment System**
- Monthly performance evaluations
- Digital signature integration
- Multi-level review process
- Automated notifications
- Performance analytics

### **Reporting & Analytics**
- Student progress reports
- Supervisor workload analytics
- State-wide statistics
- Export functionality
- Real-time dashboards

## 🔒 **Security Features**

- ✅ Role-based access control
- ✅ Secure password hashing
- ✅ File upload validation
- ✅ SQL injection prevention
- ✅ XSS protection
- ✅ CSRF protection

## 🚀 **Production Deployment**

### **Environment Variables**
```bash
FLASK_ENV=production
SECRET_KEY=your-secret-key
DATABASE_URL=your-database-url
```

### **Recommended Setup**
- Use PostgreSQL for production database
- Configure proper file storage (AWS S3, etc.)
- Set up SSL/HTTPS
- Configure email notifications
- Implement backup strategy

## 🤝 **Contributing**

1. Follow the existing code structure
2. Update documentation for new features
3. Test with provided test accounts
4. Maintain security best practices

## 📞 **Support**

For technical support or feature requests:
- Check documentation files
- Review test account credentials
- Verify database setup
- Check application logs

## 📄 **License**

This project is developed for educational and institutional use in managing SIWES programs.

---

**🎉 Ready to manage your SIWES program efficiently!**

**Access the application at: http://localhost:5000**
