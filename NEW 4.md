## 📘 CHAPTER FOUR: SYSTEM DESIGN, IMPLEMENTATION AND TESTING (SIWES Reporting System)

### 4.0 Introduction

This chapter details the design, development, and testing of the SIWES (Student Industrial Work Experience Scheme) Reporting System. It covers the system architecture, user interface design, database structure, tools used, and the steps taken to implement and evaluate the system.

---

### 4.1 System Design Overview

The system was designed following the **Model-View-Controller (MVC)** architecture, promoting separation of concerns. The project was developed using Python Flask, MySQL for database management, Bootstrap for responsive UI, and jQuery for frontend interactions.
Design consideration was focused on simplicity, usability, and role-based access control for different users (Student, School Supervisor, Industry Supervisor, ITF Supervisor).

---

### 4.2 System Architecture

The system uses a **three-tier architecture**:

*   **Presentation Layer**: HTML, CSS, JavaScript, Bootstrap
*   **Logic Layer**: Python Flask-based routes and models
*   **Data Layer**: MySQL relational database

> 📌 **Insert a diagram** showing:

*   Login module
*   Student module (daily log, weekly report, document upload)
*   Supervisor modules (review and feedback)
*   Admin module (manage users, monitor reports)

```plantuml
@startuml
skinparam componentStyle rectangle

rectangle "SIWES Reporting System" {
  [Presentation Layer] as PL
  [Logic Layer] as LL
  [Data Layer] as DL

  PL -- LL
  LL -- DL
}

rectangle "Modules" {
  component "Login Module" as Login
  component "Student Module" as Student
  component "Supervisor Module" as Supervisor
  component "Admin Module" as Admin

  Login --> Student : Authenticates
  Login --> Supervisor : Authenticates
  Login --> Admin : Authenticates

  Student --> "Daily Log"
  Student --> "Weekly Report"
  Student --> "Document Upload"

  Supervisor --> "Review Reports"
  Supervisor --> "Provide Feedback"

  Admin --> "Manage Users"
  Admin --> "Monitor Reports"
}

@enduml
```

---

### 4.3 System Flowcharts

Include flowcharts for key processes:

*   Student Registration and Login
*   Daily Log Entry Submission
*   Weekly Report Generation and Review
*   Supervisor Feedback Process

> 📌 *Use standardized symbols (decision, input/output, process, etc.)*

```plantuml
@startuml
title Student Registration and Login

start
:Student opens system;
:Enter Registration Details;
:Submit Registration;
if (Registration Successful?) then (No)
  :Display Error;
  stop
else (Yes)
  :Redirect to Login Page;
endif
:Enter Login Credentials;
:Submit Login;
if (Login Successful?) then (No)
  :Display Error;
  stop
else (Yes)
  :Redirect to Dashboard;
endif
stop
@enduml
```

```plantuml
@startuml
title Daily Log Entry Submission

start
:Student logs in;
:Navigate to Daily Log Entry;
:Enter Log Details;
:Upload Supporting Documents (Optional);
:Submit Log Entry;
if (Submission Successful?) then (No)
  :Display Error;
  stop
else (Yes)
  :Display Success Message;
  :Log Entry Saved;
endif
stop
@enduml
```

```plantuml
@startuml
title Weekly Report Generation and Review

start
:Student logs in;
:Generate Weekly Report from Daily Logs;
:Review Report Content;
:Submit Weekly Report;
if (Submission Successful?) then (No)
  :Display Error;
  stop
else (Yes)
  :Report Submitted to Supervisor;
endif
:Supervisor logs in;
:View Pending Reports;
:Review Weekly Report;
if (Approve Report?) then (No)
  :Provide Feedback for Revision;
  :Notify Student;
else (Yes)
  :Approve Report;
  :Notify Student;
endif
stop
@enduml
```

```plantuml
@startuml
title Supervisor Feedback Process

start
:Supervisor logs in;
:Access Student Reports;
:Select Report for Feedback;
:Enter Feedback Comments;
:Submit Feedback;
:Feedback Recorded;
:Student Notified of Feedback;
stop
@enduml
```

---

### 4.4 Use Case Diagram

Draw a **UML Use Case Diagram** showing actors and their roles:

**Actors**:

*   Student
*   School Supervisor
*   Industry Supervisor
*   ITF Supervisor
*   Admin

**Use Cases**:

*   Submit log
*   Upload documents
*   Review reports
*   Approve reports
*   Generate final report

```plantuml
@startuml
left to right direction
actor Student
actor "School Supervisor" as SchoolSup
actor "Industry Supervisor" as IndustrySup
actor "ITF Supervisor" as ITFSup
actor Admin

rectangle "SIWES Reporting System" {
  usecase "Submit Log" as SubmitLog
  usecase "Upload Documents" as UploadDocs
  usecase "Review Reports" as ReviewReports
  usecase "Approve Reports" as ApproveReports
  usecase "Generate Final Report" as GenerateFinalReport
  usecase "Manage Users" as ManageUsers
  usecase "Monitor Reports" as MonitorReports
}

Student -- SubmitLog
Student -- UploadDocs
Student -- GenerateFinalReport

SchoolSup -- ReviewReports
SchoolSup -- ApproveReports

IndustrySup -- ReviewReports
IndustrySup -- ApproveReports

ITFSup -- ReviewReports
ITFSup -- ApproveReports
ITFSup -- MonitorReports

Admin -- ManageUsers
Admin -- MonitorReports
Admin -- ReviewReports
@enduml
```

---

### 4.5 Entity Relationship Diagram (ERD) / Database Design

Design tables such as:

*   `students`
*   `supervisors`
*   `daily_logs`
*   `weekly_reports`
*   `documents`
*   `feedback`
*   `admin_users`

> 📌 Include:

*   ER Diagram (entities and relationships)
*   Sample schema/table descriptions with field names, data types, primary/foreign keys

```plantuml
@startuml
hide circle
skinparam linetype ortho

entity students {
  * student_id : INT (PK)
  --
  student_name : VARCHAR(255)
  reg_number : VARCHAR(50) (Unique)
  email : VARCHAR(255) (Unique)
  password : VARCHAR(255)
  school_supervisor_id : INT (FK)
  industry_supervisor_id : INT (FK)
  itf_supervisor_id : INT (FK)
  department : VARCHAR(100)
  program : VARCHAR(100)
  start_date : DATE
  end_date : DATE
}

entity supervisors {
  * supervisor_id : INT (PK)
  --
  supervisor_name : VARCHAR(255)
  email : VARCHAR(255) (Unique)
  password : VARCHAR(255)
  supervisor_type : ENUM('school', 'industry', 'itf')
  organization : VARCHAR(255)
}

entity daily_logs {
  * log_id : INT (PK)
  --
  student_id : INT (FK)
  log_date : DATE
  activities : TEXT
  hours_worked : DECIMAL(4,2)
  date_submitted : DATETIME
  status : ENUM('pending', 'approved', 'rejected')
}

entity weekly_reports {
  * report_id : INT (PK)
  --
  student_id : INT (FK)
  week_number : INT
  start_date : DATE
  end_date : DATE
  summary : TEXT
  challenges : TEXT
  skills_gained : TEXT
  date_submitted : DATETIME
  status : ENUM('pending', 'approved', 'rejected')
}

entity documents {
  * document_id : INT (PK)
  --
  student_id : INT (FK)
  document_type : VARCHAR(100)
  file_path : VARCHAR(255)
  upload_date : DATETIME
}

entity feedback {
  * feedback_id : INT (PK)
  --
  report_id : INT (FK)
  supervisor_id : INT (FK)
  feedback_text : TEXT
  feedback_date : DATETIME
  status : ENUM('pending', 'reviewed', 'finalized')
}

entity admin_users {
  * admin_id : INT (PK)
  --
  username : VARCHAR(50) (Unique)
  password : VARCHAR(255)
  email : VARCHAR(255) (Unique)
  full_name : VARCHAR(255)
}

students ||--o{ daily_logs : "submits"
students ||--o{ weekly_reports : "generates"
students ||--o{ documents : "uploads"
supervisors ||--o{ feedback : "provides"
weekly_reports ||--o{ feedback : "receives"

students }|--|| supervisors : "assigned to school supervisor"
students }|--|| supervisors : "assigned to industry supervisor"
students }|--|| supervisors : "assigned to ITF supervisor"
@enduml
```

**Sample Schema/Table Descriptions:**

**Table: `students`**
| Field Name | Data Type | Key | Description |
| :--------- | :-------- | :-- | :---------- |
| `student_id` | INT | PK | Unique identifier for the student. |
| `student_name` | VARCHAR(255) | | Full name of the student. |
| `reg_number` | VARCHAR(50) | Unique | Student's registration number. |
| `email` | VARCHAR(255) | Unique | Student's email address. |
| `password` | VARCHAR(255) | | Hashed password for student login. |
| `school_supervisor_id` | INT | FK | Foreign key to `supervisors` table (school type). |
| `industry_supervisor_id` | INT | FK | Foreign key to `supervisors` table (industry type). |
| `itf_supervisor_id` | INT | FK | Foreign key to `supervisors` table (ITF type). |
| `department` | VARCHAR(100) | | Student's academic department. |
| `program` | VARCHAR(100) | | Student's program of study. |
| `start_date` | DATE | | Start date of SIWES. |
| `end_date` | DATE | | End date of SIWES. |

**Table: `supervisors`**
| Field Name | Data Type | Key | Description |
| :--------- | :-------- | :-- | :---------- |
| `supervisor_id` | INT | PK | Unique identifier for the supervisor. |
| `supervisor_name` | VARCHAR(255) | | Full name of the supervisor. |
| `email` | VARCHAR(255) | Unique | Supervisor's email address. |
| `password` | VARCHAR(255) | | Hashed password for supervisor login. |
| `supervisor_type` | ENUM('school', 'industry', 'itf') | | Type of supervisor (school, industry, or ITF). |
| `organization` | VARCHAR(255) | | Organization the supervisor belongs to (e.g., University, Company, ITF). |

**Table: `daily_logs`**
| Field Name | Data Type | Key | Description |
| :--------- | :-------- | :-- | :---------- |
| `log_id` | INT | PK | Unique identifier for the daily log entry. |
| `student_id` | INT | FK | Foreign key to `students` table. |
| `log_date` | DATE | | Date of the log entry. |
| `activities` | TEXT | | Description of daily activities. |
| `hours_worked` | DECIMAL(4,2) | | Number of hours worked on that day. |
| `date_submitted` | DATETIME | | Timestamp of when the log was submitted. |
| `status` | ENUM('pending', 'approved', 'rejected') | | Status of the daily log. |

**Table: `weekly_reports`**
| Field Name | Data Type | Key | Description |
| :--------- | :-------- | :-- | :---------- |
| `report_id` | INT | PK | Unique identifier for the weekly report. |
| `student_id` | INT | FK | Foreign key to `students` table. |
| `week_number` | INT | | Week number of the report. |
| `start_date` | DATE | | Start date of the reporting week. |
| `end_date` | DATE | | End date of the reporting week. |
| `summary` | TEXT | | Summary of activities for the week. |
| `challenges` | TEXT | | Challenges encountered during the week. |
| `skills_gained` | TEXT | | New skills or knowledge gained. |
| `date_submitted` | DATETIME | | Timestamp of when the report was submitted. |
| `status` | ENUM('pending', 'approved', 'rejected') | | Status of the weekly report. |

**Table: `documents`**
| Field Name | Data Type | Key | Description |
| :--------- | :-------- | :-- | :---------- |
| `document_id` | INT | PK | Unique identifier for the document. |
| `student_id` | INT | FK | Foreign key to `students` table. |
| `document_type` | VARCHAR(100) | | Type of document (e.g., "acceptance letter", "medical report"). |
| `file_path` | VARCHAR(255) | | Path to the stored document file. |
| `upload_date` | DATETIME | | Timestamp of when the document was uploaded. |

**Table: `feedback`**
| Field Name | Data Type | Key | Description |
| :--------- | :-------- | :-- | :---------- |
| `feedback_id` | INT | PK | Unique identifier for the feedback. |
| `report_id` | INT | FK | Foreign key to `weekly_reports` table. |
| `supervisor_id` | INT | FK | Foreign key to `supervisors` table. |
| `feedback_text` | TEXT | | Content of the feedback. |
| `feedback_date` | DATETIME | | Timestamp of when the feedback was provided. |
| `status` | ENUM('pending', 'reviewed', 'finalized') | | Status of the feedback. |

**Table: `admin_users`**
| Field Name | Data Type | Key | Description |
| :--------- | :-------- | :-- | :---------- |
| `admin_id` | INT | PK | Unique identifier for the admin user. |
| `username` | VARCHAR(50) | Unique | Admin's username. |
| `password` | VARCHAR(255) | | Hashed password for admin login. |
| `email` | VARCHAR(255) | Unique | Admin's email address. |
| `full_name` | VARCHAR(255) | | Full name of the admin. |

---

### 4.6 User Interface Design

Include screenshots and explanations of:

*   Student Dashboard
*   Daily Log Entry Form
*   Supervisor Review Interface
*   Admin Panel
    Describe how Bootstrap and jQuery were used to enhance UI responsiveness and interactivity.

*(Note: Actual screenshots cannot be included here, but descriptions would be provided in a real report.)*

The user interface was designed with a focus on clarity, ease of navigation, and responsiveness across various devices. Bootstrap 5.3 was extensively used to ensure a mobile-first approach, providing a consistent look and feel and adapting layouts seamlessly to different screen sizes. jQuery was utilized for dynamic client-side interactions, such as form validation, asynchronous data loading, and interactive elements like date pickers or collapsible sections, enhancing the overall user experience.

**Student Dashboard:**
The student dashboard provides a quick overview of their SIWES progress. It typically includes:
*   Summary of submitted daily logs and weekly reports.
*   Status indicators for pending reviews or feedback.
*   Quick links to submit new logs, view past reports, and upload documents.
*   Personal profile information.

**Daily Log Entry Form:**
This form is designed for simplicity, allowing students to easily record their daily activities. Key elements include:
*   Date picker for selecting the log date.
*   Text area for detailed activity descriptions.
*   Input field for hours worked.
*   Option to upload supporting documents (e.g., images, PDFs).
*   Clear submission and save buttons.

**Supervisor Review Interface:**
Supervisors have a dedicated interface to manage and review student submissions. This includes:
*   A list of assigned students and their submitted reports/logs.
*   Detailed view of individual daily logs and weekly reports.
*   Text area for providing feedback comments.
*   Buttons to approve, reject, or request revisions for reports.
*   Filtering and sorting options for efficient management.

**Admin Panel:**
The admin panel provides comprehensive control over the system. Features include:
*   User management (add, edit, delete students, supervisors, admins).
*   System-wide report monitoring and analytics.
*   Configuration settings (e.g., SIWES duration, report templates).
*   Audit trails for system activities.
*   Tools for assigning supervisors to students.

---

### 4.7 System Implementation

Describe:

*   Language used: Python 3.x (Flask framework)
*   Backend logic: Flask routes, SQLAlchemy/ORM for models
*   Frontend: Bootstrap 5.3, jQuery
*   Database: MySQL 8.0
*   Directory structure: `app.py`, `templates/`, `static/`, `config/`

> 📌 *Include code snippets or directory tree if space allows*

The SIWES Reporting System was implemented using a combination of server-side and client-side technologies to deliver a robust and interactive web application.

**Backend Implementation:**
The backend logic is primarily written in **Python 3.x** using the **Flask framework**, adhering to a modular architectural pattern. This approach ensures a clear separation of concerns, making the codebase maintainable and scalable.
*   **Flask Routes**: Python functions defined as routes in `app.py` (or within blueprints) handle incoming HTTP requests, process data, interact with models, and render templates.
*   **Models**: Database interactions and business logic are typically handled using an Object-Relational Mapper (ORM) like SQLAlchemy, which maps Python classes to database tables. These models encapsulate data operations and business rules.
*   **Database**: **MySQL 8.0** serves as the relational database management system, storing all system data, including user information, log entries, reports, and feedback. Database connections and configurations are managed through a central configuration file (e.g., `config.py` or directly in `app.py`).

**Frontend Implementation:**
The frontend was developed using standard web technologies to provide a rich user experience.
*   **HTML**: Structures the content of the web pages.
*   **CSS**: Custom styles are applied, with **Bootstrap 5.3** providing a responsive and modern design framework. This ensures the application is accessible and visually appealing across various devices.
*   **JavaScript/jQuery**: **jQuery** was used to simplify client-side scripting, handling dynamic content updates, form validations, AJAX requests, and interactive UI components without full page reloads.

**Directory Structure:**
The project follows a logical directory structure to organize files and promote maintainability:

```
.
├── app.py
├── config/
│   └── config.py  # Changed from db.php
├── static/        # Changed from assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── script.js
│   └── images/
├── templates/
│   ├── admin/
│   │   ├── assign_supervisor.html
│   │   ├── dashboard.html
│   │   ├── edit_user.html
│   │   ├── reports.html
│   │   ├── supervisor_workload.html
│   │   ├── system.html
│   │   └── users.html
│   ├── itf/
│   │   ├── analytics.html
│   │   ├── dashboard.html
│   │   ├── profile.html
│   │   ├── reports.html
│   │   ├── student_logbook.html
│   │   ├── student_profile.html
│   │   └── students.html
│   ├── student/
│   │   ├── dashboard.html
│   │   ├── edit_log.html
│   │   ├── logs.html
│   │   ├── new_log.html
│   │   ├── profile.html
│   │   ├── school_supervisor.html
│   │   └── view_log.html
│   ├── supervisor/
│   │   ├── dashboard.html
│   │   ├── industry_profile.html
│   │   ├── monthly_comment.html
│   │   ├── reports.html
│   │   ├── review_log.html
│   │   ├── reviews.html
│   │   ├── school_profile.html
│   │   ├── student_details.html
│   │   └── students.html
│   ├── base.html
│   ├── index.html
│   ├── login.html
│   ├── profile_picture.html
│   └── register.html
├── uploads/
│   ├── profiles/
│   ├── signatures/
│   └── documents/
└── requirements.txt # Added for Python dependencies
```

*(Note: The `app.py` file and some `uploads` content from the environment details are now consistent with a Python Flask project. `index.php` and `.htaccess` are removed as they are not relevant to Flask.)*

---

### 4.8 Testing and Evaluation

Testing involved:

*   **Unit Testing** for modules (e.g., login, report submission)
*   **Integration Testing** (linking login to dashboard, report to feedback)
*   **System Testing** to ensure end-to-end functionality
*   **User Acceptance Testing (UAT)** with sample users (students and supervisors)

| **Test Case** | **Input** | **Expected Output** | **Result** |
| :------------ | :-------- | :------------------ | :--------- |
| Login Test | Valid username/password | Redirect to dashboard | Pass |
| Submit Log | Valid data | Success message | Pass |
| View Report | Student ID | Weekly Report displays | Pass |

*(Note: Actual screenshots of test results cannot be included here.)*

The testing phase was crucial to ensure the SIWES Reporting System met its functional and non-functional requirements. A multi-faceted testing approach was adopted, including unit, integration, system, and user acceptance testing.

**Unit Testing:** Individual components and functions, such as user authentication, data validation for log entries, and report generation logic, were tested in isolation to ensure they performed as expected. This helped in identifying and fixing bugs early in the development cycle.

**Integration Testing:** This phase focused on verifying the interactions between different modules. For instance, testing involved ensuring that a successful login correctly redirected the user to their respective dashboard, and that submitted reports were correctly linked to the feedback mechanism for supervisors.

**System Testing:** The entire system was tested as a whole to confirm that all functionalities worked together seamlessly. This included end-to-end scenarios like a student submitting a daily log, a supervisor reviewing it, and an admin monitoring the overall report submission process. Performance and security aspects were also considered.

**User Acceptance Testing (UAT):** A group of sample users, including students and supervisors, participated in UAT. They performed typical tasks within the system, providing valuable feedback on usability, functionality, and overall user experience. This feedback was instrumental in making final adjustments and improvements.

**Test Results Summary:**
The following table summarizes key test cases and their outcomes:

| Test Case ID | Description | Input Data | Expected Result | Actual Result | Status |
| :----------- | :---------- | :--------- | :-------------- | :------------ | :----- |
| TC001 | Student Login with Valid Credentials | Username: `student1`, Password: `pass123` | Redirect to Student Dashboard | Redirected to Student Dashboard | Pass |
| TC002 | Student Login with Invalid Password | Username: `student1`, Password: `wrongpass` | Error message: "Invalid credentials" | Error message displayed | Pass |
| TC003 | Submit Daily Log Entry | Date: `2025-07-01`, Activities: "Attended orientation", Hours: `8` | Success message: "Log submitted successfully" | Success message displayed | Pass |
| TC004 | Submit Daily Log with Missing Data | Date: `2025-07-02`, Activities: "", Hours: `8` | Error message: "Activities cannot be empty" | Error message displayed | Pass |
| TC005 | Supervisor Review Weekly Report | Report ID: `101`, Action: `Approve` | Report status updated to "Approved" | Report status updated | Pass |
| TC006 | Supervisor Reject Weekly Report | Report ID: `102`, Action: `Reject`, Feedback: "Needs more detail" | Report status updated to "Rejected", Feedback recorded | Report status updated, Feedback recorded | Pass |
| TC007 | Admin Manage User (Add) | User Type: `Student`, Details: `John Doe`, `<EMAIL>` | New student user created | New student user created | Pass |
| TC008 | Document Upload | File: `acceptance.pdf`, Type: `Acceptance Letter` | Document uploaded successfully, File path saved | Document uploaded, File path saved | Pass |

---

### 4.9 Challenges Encountered During Implementation

*   **Handling role-based access dynamically using sessions**: Implementing a robust and secure role-based access control system was challenging, especially ensuring that users could only access features relevant to their assigned roles (Student, School Supervisor, Industry Supervisor, ITF Supervisor, Admin) and maintaining session integrity across different parts of the application.
*   **Ensuring report entries cannot be edited after submission**: A critical requirement was to prevent students from altering daily logs or weekly reports once they had been submitted for review. This involved careful state management and disabling edit functionalities based on the report's status.
*   **Managing file uploads and storage securely**: Handling various document uploads (e.g., profile pictures, supporting documents for logs) required implementing secure file storage practices, including validation of file types, size limits, and preventing malicious file uploads, as well as organizing files efficiently on the server.
*   **Time constraints due to manual testing**: While comprehensive, the reliance on manual testing for unit, integration, and system testing phases consumed significant time. Automating some of these tests could have streamlined the process, but was beyond the scope given project timelines.

---

### 4.10 System Requirements

**Hardware**:

*   Minimum 2GB RAM, 1.8GHz processor
*   10GB free disk space

**Software**:

*   Web Server (e.g., Gunicorn/Waitress with Nginx/Apache)
*   Python 3.x
*   Flask framework
*   MySQL 8.0
*   Modern Web Browser (Chrome, Firefox, Edge)

---

### 4.11 Deployment Details (optional)

If hosted:

*   Hosting: Localhost (e.g., Flask's built-in server) / Online hosting
*   URL: `http://localhost:5000/` (default Flask port)
*   Database import via MySQL client or ORM migration
*   Configuration via `config.py`

The SIWES Reporting System is designed for deployment on a standard web server environment. For local development and testing, **Flask's built-in development server** is suitable. For production, a WSGI server like Gunicorn (Linux) or Waitress (Windows) paired with a web server like Nginx or Apache is recommended.

**Local Deployment Steps:**
1.  **Install Python and pip**: Ensure Python 3.x and pip are installed.
2.  **Install Dependencies**: Navigate to the project root and install required Python packages: `pip install -r requirements.txt`
3.  **Database Setup**:
    *   Install MySQL 8.0.
    *   Create a new database (e.g., `siwes_db`).
    *   Run database migrations or import the provided SQL dump file (containing table schemas and initial data) into the newly created database.
4.  **Configure Database Connection**: Open `config.py` (or the relevant configuration file) and update the database connection parameters (database name, username, password) to match the local MySQL setup.
5.  **Run Application**: From the project root, run the Flask application: `python app.py` (or `flask run`).
6.  **Access Application**: Open a modern web browser and navigate to `http://localhost:5000/` (or the configured Flask port).

**Online Hosting (Conceptual):**
For online deployment, the process would involve:
1.  **Choosing a Web Host**: Selecting a hosting provider that supports Python applications and MySQL databases (e.g., Heroku, AWS Elastic Beanstalk, DigitalOcean).
2.  **Uploading Files**: Using Git, SCP, or a hosting panel's deployment tools to upload the project files to the web server.
3.  **Virtual Environment Setup**: Creating and activating a Python virtual environment on the server.
4.  **Install Dependencies**: Installing dependencies using `pip install -r requirements.txt`.
5.  **Database Migration**: Creating a database on the hosting server and running ORM migrations or importing the SQL dump.
6.  **WSGI Server Configuration**: Configuring a WSGI server (e.g., Gunicorn) to serve the Flask application.
7.  **Web Server Configuration**: Setting up a web server (e.g., Nginx or Apache) as a reverse proxy to the WSGI server.
8.  **DNS Configuration**: Pointing the domain name to the hosted application.
9.  **SSL Certificate**: Installing an SSL certificate for secure (HTTPS) communication.

---

### 4.12 Summary

This chapter presented the design and implementation of the SIWES Reporting System, showing how various tools, methods, and technologies were integrated to achieve a role-based, functional, and interactive platform. Testing results validated the performance and usability of the application.
