{% extends "base.html" %}

{% block title %}Assign Supervisors - Admin Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users me-2"></i>Manage Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('assign_supervisor') }}">
                    <i class="fas fa-user-tie me-2"></i>Assign Supervisors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_system') }}">
                    <i class="fas fa-cog me-2"></i>System Settings
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-tie me-2"></i>Assign Supervisors
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <form method="POST" action="{{ url_for('auto_assign_all_students') }}" style="display: inline;">
                <button type="submit" class="btn btn-success" onclick="return confirm('Auto-assign supervisors to all unassigned students using least loaded algorithm?')">
                    <i class="fas fa-magic me-2"></i>Auto-Assign All
                </button>
            </form>
        </div>
        <div class="btn-group">
            <a href="{{ url_for('supervisor_workload') }}" class="btn btn-outline-info">
                <i class="fas fa-chart-bar me-2"></i>View Workload
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>Assign Supervisor to Student
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="student_id" class="form-label">Select Student</label>
                        <select class="form-select" id="student_id" name="student_id" required onchange="loadMatchingSupervisors()">
                            <option value="">Choose a student...</option>
                            {% for student in students %}
                            <option value="{{ student.id }}"
                                    data-institution="{{ student.institution or '' }}"
                                    data-department="{{ student.department or '' }}"
                                    data-organization="{{ student.siwes_organization or '' }}">
                                {{ student.user.name }} ({{ student.matric_number }}) -
                                {{ student.institution or 'No Institution' }}, {{ student.department or 'No Department' }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Select a student to see matching supervisors</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="industry_supervisor_id" class="form-label">Industry Supervisor</label>
                            <select class="form-select" id="industry_supervisor_id" name="industry_supervisor_id">
                                <option value="">Select Industry Supervisor...</option>
                                {% for user, profile in industry_supervisors %}
                                <option value="{{ user.id }}">
                                    {{ user.name }} - {{ profile.company_name }} ({{ profile.industry_type }})
                                </option>
                                {% endfor %}
                                {% for user in industry_users_without_profile %}
                                <option value="{{ user.id }}" class="text-muted">
                                    {{ user.name }} ({{ user.email }}) - Profile Incomplete
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Supervisor from the organization where student is placed.</div>
                            <div id="industry-matches" class="mt-2" style="display: none;">
                                <small class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <span id="industry-match-text"></span>
                                </small>
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="school_supervisor_id" class="form-label">School Supervisor</label>
                            <select class="form-select" id="school_supervisor_id" name="school_supervisor_id">
                                <option value="">Select School Supervisor...</option>
                                {% for user, profile in school_supervisors %}
                                <option value="{{ user.id }}"
                                        data-institution="{{ profile.institution }}"
                                        data-department="{{ profile.department }}">
                                    {{ user.name }} - {{ profile.institution }}, {{ profile.department }}
                                </option>
                                {% endfor %}
                                {% for user in school_users_without_profile %}
                                <option value="{{ user.id }}" class="text-muted">
                                    {{ user.name }} ({{ user.email }}) - Profile Incomplete
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Academic supervisor from the student's institution.</div>
                            <div id="school-matches" class="mt-2" style="display: none;">
                                <small class="text-success">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <span id="school-match-text"></span>
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Assign Supervisors
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Assignment Guidelines
                </h5>
            </div>
            <div class="card-body">
                <h6>Auto-Assignment Algorithm</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-magic text-primary me-2"></i>Least Loaded Assignment</li>
                    <li><i class="fas fa-balance-scale text-success me-2"></i>Balanced workload distribution</li>
                    <li><i class="fas fa-filter text-info me-2"></i>Smart matching criteria</li>
                    <li><i class="fas fa-clock text-warning me-2"></i>Instant assignment</li>
                </ul>

                <h6 class="mt-3">School Supervisor Matching</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-university text-success me-2"></i>Same institution</li>
                    <li><i class="fas fa-graduation-cap text-success me-2"></i>Same department</li>
                    <li><i class="fas fa-user-check text-success me-2"></i>Lowest student count</li>
                    <li><i class="fas fa-id-card text-success me-2"></i>Complete profile</li>
                </ul>

                <h6 class="mt-3">Industry Supervisor Matching</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-building text-success me-2"></i>Available supervisors</li>
                    <li><i class="fas fa-user-check text-success me-2"></i>Lowest student count</li>
                    <li><i class="fas fa-id-card text-success me-2"></i>Complete profile</li>
                    <li><i class="fas fa-handshake text-success me-2"></i>Active status</li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Assignment Statistics
                </h5>
            </div>
            <div class="card-body">
                {% set total_students = students|length %}
                {% set with_industry = students|selectattr('industry_supervisor_id')|list|length %}
                {% set with_school = students|selectattr('school_supervisor_id')|list|length %}
                {% set fully_assigned = students|selectattr('industry_supervisor_id')|selectattr('school_supervisor_id')|list|length %}

                <p><strong>Total Students:</strong> {{ total_students }}</p>
                <p><strong>With Industry Supervisor:</strong> {{ with_industry }}</p>
                <p><strong>With School Supervisor:</strong> {{ with_school }}</p>
                <p><strong>Fully Assigned:</strong> {{ fully_assigned }}</p>

                {% if total_students > 0 %}
                <div class="progress mb-2">
                    <div class="progress-bar bg-success" style="width: {{ (fully_assigned / total_students * 100)|round(1) }}%">
                        {{ (fully_assigned / total_students * 100)|round(1) }}%
                    </div>
                </div>
                <small class="text-muted">Students with both supervisors assigned</small>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Current Assignments
                </h5>
            </div>
            <div class="card-body">
                {% if students %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Student</th>
                                <th>Department</th>
                                <th>Industry Supervisor</th>
                                <th>School Supervisor</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students %}
                            <tr>
                                <td>
                                    <strong>{{ student.user.name }}</strong><br>
                                    <small class="text-muted">{{ student.matric_number }}</small>
                                </td>
                                <td>{{ student.department or 'Not specified' }}</td>
                                <td>
                                    {% if student.industry_supervisor %}
                                        {% set industry_profile = student.industry_supervisor.industry_supervisor_profile %}
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-success me-2">{{ student.industry_supervisor.name }}</span>
                                            {% if industry_profile %}
                                                <small class="text-muted">{{ industry_profile.company_name }}</small>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="badge bg-warning">Not assigned</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if student.school_supervisor %}
                                        {% set school_profile = student.school_supervisor.school_supervisor_profile %}
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-success me-2">{{ student.school_supervisor.name }}</span>
                                            {% if school_profile %}
                                                <small class="text-muted">{{ school_profile.institution }}</small>
                                            {% endif %}
                                        </div>
                                    {% else %}
                                        <span class="badge bg-warning">Not assigned</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if student.industry_supervisor and student.school_supervisor %}
                                        <span class="badge bg-success">Complete</span>
                                    {% elif student.industry_supervisor or student.school_supervisor %}
                                        <span class="badge bg-warning">Partial</span>
                                    {% else %}
                                        <span class="badge bg-danger">None</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" onclick="editAssignment({{ student.id }})" title="Manual Assignment">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" action="{{ url_for('reassign_student_supervisors', student_id=student.id) }}" style="display: inline;">
                                            <button type="submit" class="btn btn-outline-success"
                                                    onclick="return confirm('Auto-assign supervisors for {{ student.user.name }}?')"
                                                    title="Auto-Assign">
                                                <i class="fas fa-magic"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Students Found</h5>
                    <p class="text-muted">No students are registered in the system yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function editAssignment(studentId) {
    // Pre-fill the form with the student's current assignments
    const studentSelect = document.getElementById('student_id');
    studentSelect.value = studentId;
    loadMatchingSupervisors();

    // Scroll to the form
    document.querySelector('.card').scrollIntoView({ behavior: 'smooth' });

    // Highlight the form
    const form = document.querySelector('.card');
    form.style.border = '2px solid #007bff';
    setTimeout(() => {
        form.style.border = '';
    }, 2000);
}

function loadMatchingSupervisors() {
    const studentSelect = document.getElementById('student_id');
    const selectedOption = studentSelect.options[studentSelect.selectedIndex];

    if (!selectedOption.value) {
        hideMatchingIndicators();
        return;
    }

    const studentInstitution = selectedOption.dataset.institution;
    const studentDepartment = selectedOption.dataset.department;
    const studentOrganization = selectedOption.dataset.organization;

    // Highlight matching school supervisors
    highlightMatchingSchoolSupervisors(studentInstitution, studentDepartment);

    // Show matching indicators
    showMatchingIndicators(studentInstitution, studentDepartment, studentOrganization);
}

function highlightMatchingSchoolSupervisors(studentInstitution, studentDepartment) {
    const schoolSelect = document.getElementById('school_supervisor_id');
    const options = schoolSelect.options;

    for (let i = 1; i < options.length; i++) { // Skip first option
        const option = options[i];
        const supervisorInstitution = option.dataset.institution;
        const supervisorDepartment = option.dataset.department;

        if (supervisorInstitution === studentInstitution && supervisorDepartment === studentDepartment) {
            option.style.backgroundColor = '#d4edda';
            option.style.fontWeight = 'bold';
            option.innerHTML = '✓ ' + option.innerHTML.replace('✓ ', '');
        } else {
            option.style.backgroundColor = '';
            option.style.fontWeight = '';
            option.innerHTML = option.innerHTML.replace('✓ ', '');
        }
    }
}

function showMatchingIndicators(institution, department, organization) {
    const schoolMatches = document.getElementById('school-matches');
    const schoolMatchText = document.getElementById('school-match-text');
    const industryMatches = document.getElementById('industry-matches');
    const industryMatchText = document.getElementById('industry-match-text');

    if (institution && department) {
        schoolMatches.style.display = 'block';
        schoolMatchText.textContent = `Looking for supervisors from ${institution}, ${department}`;
    } else {
        schoolMatches.style.display = 'none';
    }

    if (organization) {
        industryMatches.style.display = 'block';
        industryMatchText.textContent = `Student is at ${organization}`;
    } else {
        industryMatches.style.display = 'none';
    }
}

function hideMatchingIndicators() {
    document.getElementById('school-matches').style.display = 'none';
    document.getElementById('industry-matches').style.display = 'none';

    // Reset school supervisor highlighting
    const schoolSelect = document.getElementById('school_supervisor_id');
    const options = schoolSelect.options;

    for (let i = 1; i < options.length; i++) {
        const option = options[i];
        option.style.backgroundColor = '';
        option.style.fontWeight = '';
        option.innerHTML = option.innerHTML.replace('✓ ', '');
    }
}

// Auto-populate supervisors when student is selected
document.getElementById('student_id').addEventListener('change', loadMatchingSupervisors);
</script>
{% endblock %}
