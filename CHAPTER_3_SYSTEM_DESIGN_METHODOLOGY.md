# CHAPTER THREE

## SYSTEM DESIGN AND METHODOLOGY

### 3.1. Introduction

This chapter outlines the design and methodology used in the development of the Online Industrial Training Logbook System. It covers the system architecture, the design of the database, the choice of technologies, and the development methodology employed. The design is focused on creating a robust, scalable, and user-friendly system that meets the needs of all stakeholders: students, supervisors (both school and industry-based), ITF coordinators, and system administrators.

### 3.2. System Architecture

The system is designed using a three-tier architecture, which is a well-established software architecture pattern that separates applications into three logical and physical computing tiers: the presentation tier, the application tier (or business logic), and the data tier.

*   **Presentation Tier (Frontend):** This is the user interface of the application. It is what the user sees and interacts with. It is built using HTML, CSS, and JavaScript. The frontend sends requests to the backend and displays the data returned by the backend.
*   **Application Tier (Backend):** This tier contains the business logic of the application. It processes user requests, interacts with the database, and sends responses back to the frontend. This is built using the Flask framework in Python.
*   **Data Tier (Database):** This tier is responsible for storing and managing the application's data. It consists of a database management system (DBMS). In this project, SQLite is used for its simplicity and ease of use.

```plantuml
@startuml
!theme vibrant
skinparam rectangle<<frame>> {
  borderStyle solid
  borderColor #333
  backgroundColor #f8f9fa
}

rectangle "Online Industrial Training Logbook System" <<frame>> {
  node "User (Browser)" as User

  rectangle "Web Server (Flask)" {
    rectangle "Presentation Tier" as Presentation {
      component "HTML/CSS/JS" as Frontend
    }
    rectangle "Application Tier" as Application {
      component "Flask Application" as App
      component "Business Logic" as Logic
    }
    rectangle "Data Tier" as Data {
      database "SQLite Database" as DB
    }
  }

  User --> Frontend : HTTP Requests
  Frontend --> App : Routes
  App --> Logic : Processes Requests
  Logic --> DB : CRUD Operations
  DB --> Logic : Data
  Logic --> App : Returns Data
  App --> Frontend : Renders Templates
  Frontend --> User : HTTP Responses
}
@enduml
```
**Figure 3.1: System Architecture Diagram**

### 3.3. Database Design

The database is a crucial component of the system. It is designed to store all the information related to users, logbook entries, comments, and other system data. The database schema is designed to be normalized to reduce data redundancy and improve data integrity.

The main entities in the database are:
*   **User:** Stores information about all users (students, supervisors, admins).
*   **LogEntry:** Stores the daily or weekly log entries made by students.
*   **Comment:** Stores comments made by supervisors on student log entries.
*   **Profile:** Stores additional profile information for users, like profile pictures and signatures.

```plantuml
@startuml
!theme vibrant
skinparam rectangle<<frame>> {
  borderStyle solid
  borderColor #333
  backgroundColor #f8f9fa
}

entity "User" as User {
  *id : integer (PK)
  --
  username : text
  password : text
  role : text
  first_name : text
  last_name : text
  email : text
  supervisor_id : integer (FK)
}

entity "LogEntry" as LogEntry {
  *id : integer (PK)
  --
  student_id : integer (FK)
  log_date : date
  week_number : integer
  entry_text : text
  status : text
}

entity "Comment" as Comment {
  *id : integer (PK)
  --
  log_entry_id : integer (FK)
  supervisor_id : integer (FK)
  comment_text : text
  comment_date : date
}

entity "Profile" as Profile {
  *id : integer (PK)
  --
  user_id : integer (FK)
  profile_picture : text
  signature : text
}

User "1" -- "0..*" LogEntry : "has"
User "1" -- "0..*" Comment : "makes"
LogEntry "1" -- "0..*" Comment : "has"
User "1" -- "1" Profile : "has"

@enduml
```
**Figure 3.2: Entity-Relationship Diagram**

### 3.4. Use Case Diagram

The use case diagram illustrates the interactions between the different actors (users) and the system. It shows the different functionalities available to each type of user.

```plantuml
@startuml
!theme vibrant
left to right direction

actor Student
actor Supervisor
actor Admin

rectangle "Online Logbook System" {
  usecase "Manage Profile" as UC1
  usecase "Submit Logbook" as UC2
  usecase "View Comments" as UC3
  usecase "Review Logbook" as UC4
  usecase "Add Comments" as UC5
  usecase "Manage Users" as UC6
  usecase "Assign Supervisors" as UC7
  usecase "View Reports" as UC8
}

Student --> UC1
Student --> UC2
Student --> UC3

Supervisor --> UC1
Supervisor --> UC4
Supervisor --> UC5
Supervisor --> UC8

Admin --> UC6
Admin --> UC7
Admin --> UC8
@enduml
```
**Figure 3.3: Use Case Diagram**

### 3.5. Choice of Technologies

The technologies for this project were chosen based on their suitability for developing a web-based application, their ease of use, and the availability of documentation and community support.

*   **Python:** A high-level, general-purpose programming language. Its design philosophy emphasizes code readability with the use of significant indentation.
*   **Flask:** A micro web framework written in Python. It is classified as a microframework because it does not require particular tools or libraries. It has no database abstraction layer, form validation, or any other components where pre-existing third-party libraries provide common functions.
*   **SQLite:** A C-language library that implements a small, fast, self-contained, high-reliability, full-featured, SQL database engine. SQLite is the most used database engine in the world.
*   **HTML/CSS/JavaScript:** The standard technologies for building the frontend of web applications.
*   **PlantUML:** A tool to create diagrams from a plain text language. Used for generating the diagrams in this document.

### 3.6. Development Methodology

The project was developed using the **Agile development methodology**. This approach was chosen because it allows for flexibility and iterative development. The project was broken down into smaller, manageable tasks, and each task was developed and tested in short cycles. This allowed for continuous feedback and improvement throughout the development process.

The development process involved the following phases:
1.  **Planning:** Defining the project requirements and creating a project plan.
2.  **Design:** Designing the system architecture and database schema.
3.  **Development:** Writing the code for the application.
4.  **Testing:** Testing the application to ensure it is working correctly.
5.  **Deployment:** Deploying the application to a web server.
6.  **Maintenance:** Providing ongoing support and maintenance for the application.

### 3.7. References

*   Flask Web Development, 2nd Edition by Miguel Grinberg
*   Agile Project Management for Dummies by Mark C. Layton
*   Database Systems: The Complete Book by Hector Garcia-Molina, Jeffrey D. Ullman, and Jennifer Widom
