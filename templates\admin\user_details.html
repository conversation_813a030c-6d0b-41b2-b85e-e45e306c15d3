{% extends "base.html" %}

{% block title %}User Details - {{ user.name }} - Admin Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('admin_users') }}">
                    <i class="fas fa-users me-2"></i>Manage Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('assign_supervisor') }}">
                    <i class="fas fa-user-tie me-2"></i>Assign Supervisors
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('supervisor_workload') }}">
                    <i class="fas fa-chart-bar me-2"></i>Supervisor Workload
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('admin_system') }}">
                    <i class="fas fa-cog me-2"></i>System Settings
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>User Details - {{ user.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin_edit_user', user_id=user.id) }}" class="btn btn-primary">
                <i class="fas fa-edit me-2"></i>Edit User
            </a>
        </div>
        <div class="btn-group">
            <a href="{{ url_for('admin_users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
        </div>
    </div>
</div>

<!-- User Basic Information -->
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-id-card me-2"></i>Basic Information
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    {% if user.profile_picture %}
                        <img src="{{ url_for('uploaded_profile', filename=user.profile_picture) }}"
                             alt="Profile Picture"
                             class="avatar-circle-large mx-auto mb-3">
                    {% else %}
                        <div class="avatar-circle-large mx-auto mb-3">
                            {{ user.name[0].upper() }}
                        </div>
                    {% endif %}
                    <h4>{{ user.name }}</h4>
                    <span class="badge bg-{{ 'primary' if user.role.name == 'Student' else 'success' if 'Supervisor' in user.role.name else 'warning' if user.role.name == 'ITF Supervisor' else 'danger' }} fs-6">
                        {{ user.role.name }}
                    </span>
                </div>

                <table class="table table-borderless">
                    <tr>
                        <td><strong>User ID:</strong></td>
                        <td>{{ user.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>{{ user.email }}</td>
                    </tr>
                    <tr>
                        <td><strong>Phone:</strong></td>
                        <td>{{ user.phone or 'Not provided' }}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Registered:</strong></td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('admin_edit_user', user_id=user.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>Edit User Details
                    </a>

                    {% if user.id != current_user.id %}
                        {% if user.is_active %}
                            <form method="POST" action="{{ url_for('admin_toggle_user_status', user_id=user.id) }}">
                                <button type="submit" class="btn btn-outline-warning w-100"
                                        onclick="return confirm('Deactivate this user?')">
                                    <i class="fas fa-user-slash me-2"></i>Deactivate User
                                </button>
                            </form>
                        {% else %}
                            <form method="POST" action="{{ url_for('admin_toggle_user_status', user_id=user.id) }}">
                                <button type="submit" class="btn btn-outline-success w-100"
                                        onclick="return confirm('Activate this user?')">
                                    <i class="fas fa-user-check me-2"></i>Activate User
                                </button>
                            </form>
                        {% endif %}

                        <form method="POST" action="{{ url_for('admin_delete_user', user_id=user.id) }}">
                            <button type="submit" class="btn btn-outline-danger w-100"
                                    onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone!')">
                                <i class="fas fa-trash me-2"></i>Delete User
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- Profile Information -->
        {% if profile %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-graduate me-2"></i>{{ user.role.name }} Profile
                </h5>
            </div>
            <div class="card-body">
                {% if user.role.name == 'Student' %}
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Matric Number:</strong></td>
                                    <td>{{ profile.matric_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Institution:</strong></td>
                                    <td>{{ profile.institution }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Department:</strong></td>
                                    <td>{{ profile.department }}</td>
                                </tr>
                                <tr>
                                    <td><strong>SIWES State:</strong></td>
                                    <td>{{ profile.siwes_state or 'Not specified' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Organization:</strong></td>
                                    <td>{{ profile.siwes_organization_name or 'Not specified' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Start Date:</strong></td>
                                    <td>{{ profile.siwes_start_date.strftime('%Y-%m-%d') if profile.siwes_start_date else 'Not set' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>End Date:</strong></td>
                                    <td>{{ profile.siwes_end_date.strftime('%Y-%m-%d') if profile.siwes_end_date else 'Not set' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>School Supervisor:</strong></td>
                                    <td>
                                        {% if profile.school_supervisor %}
                                            <a href="{{ url_for('admin_view_user', user_id=profile.school_supervisor.id) }}">
                                                {{ profile.school_supervisor.name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">Not assigned</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Industry Supervisor:</strong></td>
                                    <td>
                                        {% if profile.industry_supervisor %}
                                            <a href="{{ url_for('admin_view_user', user_id=profile.industry_supervisor.id) }}">
                                                {{ profile.industry_supervisor.name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">Not assigned</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                {% elif user.role.name == 'School Supervisor' %}
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Institution:</strong></td>
                                    <td>{{ profile.institution }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Department:</strong></td>
                                    <td>{{ profile.department }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Faculty:</strong></td>
                                    <td>{{ profile.faculty or 'Not specified' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Position:</strong></td>
                                    <td>{{ profile.position or 'Not specified' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Office Location:</strong></td>
                                    <td>{{ profile.office_location or 'Not specified' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Experience:</strong></td>
                                    <td>{{ profile.years_of_experience or 'Not specified' }} years</td>
                                </tr>
                                <tr>
                                    <td><strong>Specialization:</strong></td>
                                    <td>{{ profile.specialization or 'Not specified' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                {% elif user.role.name == 'Industry Supervisor' %}
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Company:</strong></td>
                                    <td>{{ profile.company_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Industry Type:</strong></td>
                                    <td>{{ profile.industry_type }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Position:</strong></td>
                                    <td>{{ profile.position }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Department:</strong></td>
                                    <td>{{ profile.department or 'Not specified' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Company Size:</strong></td>
                                    <td>{{ profile.company_size or 'Not specified' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Experience:</strong></td>
                                    <td>{{ profile.years_of_experience or 'Not specified' }} years</td>
                                </tr>
                                <tr>
                                    <td><strong>Specialization:</strong></td>
                                    <td>{{ profile.specialization or 'Not specified' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                {% elif user.role.name == 'ITF Supervisor' %}
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Assigned State:</strong></td>
                            <td>{{ profile.assigned_state }}</td>
                        </tr>
                        <tr>
                            <td><strong>Office Location:</strong></td>
                            <td>{{ profile.office_location or 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Position:</strong></td>
                            <td>{{ profile.position or 'Not specified' }}</td>
                        </tr>
                        <tr>
                            <td><strong>Experience:</strong></td>
                            <td>{{ profile.years_of_experience or 'Not specified' }} years</td>
                        </tr>
                        <tr>
                            <td><strong>Specialization:</strong></td>
                            <td>{{ profile.specialization or 'Not specified' }}</td>
                        </tr>
                    </table>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Profile Information</h5>
                <p class="text-muted">This user has not completed their profile yet.</p>
            </div>
        </div>
        {% endif %}

        <!-- Statistics -->
        {% if stats %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistics
                </h5>
            </div>
            <div class="card-body">
                {% if user.role.name == 'Student' %}
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-primary">{{ stats.total_logs }}</h3>
                                <p class="text-muted mb-0">Total Logs</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-success">{{ stats.approved_logs }}</h3>
                                <p class="text-muted mb-0">Approved</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-warning">{{ stats.pending_logs }}</h3>
                                <p class="text-muted mb-0">Pending</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-danger">{{ stats.rejected_logs }}</h3>
                                <p class="text-muted mb-0">Rejected</p>
                            </div>
                        </div>
                    </div>
                {% elif user.role.name in ['School Supervisor', 'Industry Supervisor'] %}
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-primary">{{ stats.assigned_students }}</h3>
                                <p class="text-muted mb-0">Students</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-info">{{ stats.total_student_logs or 0 }}</h3>
                                <p class="text-muted mb-0">Total Logs</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-success">{{ stats.approved_student_logs or 0 }}</h3>
                                <p class="text-muted mb-0">Approved</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <h3 class="text-warning">{{ stats.pending_student_logs or 0 }}</h3>
                                <p class="text-muted mb-0">Pending</p>
                            </div>
                        </div>
                    </div>
                {% elif user.role.name == 'ITF Supervisor' %}
                    <div class="text-center">
                        <h3 class="text-primary">{{ stats.state_students or 0 }}</h3>
                        <p class="text-muted mb-0">Students in {{ profile.assigned_state if profile else 'Assigned State' }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Assigned Students Details -->
        {% if user.role.name in ['School Supervisor', 'Industry Supervisor'] and assigned_students %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>Assigned Students ({{ assigned_students|length }})
                </h5>
            </div>
            <div class="card-body">
                <!-- Summary Cards -->
                {% if stats.institution_summary or stats.department_summary %}
                <div class="row mb-4">
                    {% if stats.institution_summary %}
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-university me-2"></i>By Institution
                                </h6>
                            </div>
                            <div class="card-body">
                                {% for institution, count in stats.institution_summary.items() %}
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>{{ institution }}</span>
                                    <span class="badge bg-primary">{{ count }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if stats.department_summary %}
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-graduation-cap me-2"></i>By Department
                                </h6>
                            </div>
                            <div class="card-body">
                                {% for department, count in stats.department_summary.items() %}
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>{{ department }}</span>
                                    <span class="badge bg-success">{{ count }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- Detailed Student List -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Student</th>
                                <th>Matric Number</th>
                                <th>Institution</th>
                                <th>Department</th>
                                <th>SIWES State</th>
                                <th>Organization</th>
                                <th>Logs</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in assigned_students %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if student.user.profile_picture %}
                                            <img src="{{ url_for('uploaded_profile', filename=student.user.profile_picture) }}"
                                                 alt="Profile" class="avatar-circle me-3">
                                        {% else %}
                                            <div class="avatar-circle me-3">
                                                {{ student.user.name[0].upper() }}
                                            </div>
                                        {% endif %}
                                        <div>
                                            <strong>{{ student.user.name }}</strong><br>
                                            <small class="text-muted">{{ student.user.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <code>{{ student.matric_number }}</code>
                                </td>
                                <td>
                                    <small>{{ student.institution or 'Not specified' }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ student.department or 'Not specified' }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ student.siwes_state or 'Not specified' }}</span>
                                </td>
                                <td>
                                    <small>{{ student.siwes_organization_name or 'Not specified' }}</small>
                                </td>
                                <td>
                                    {% set student_log_count = student.log_entries|length %}
                                    {% set approved_count = student.log_entries|selectattr('status', 'equalto', 'Approved')|list|length %}
                                    {% set pending_count = student.log_entries|selectattr('status', 'equalto', 'Pending')|list|length %}

                                    <div class="text-center">
                                        <span class="badge bg-primary">{{ student_log_count }}</span>
                                        {% if approved_count > 0 %}
                                            <span class="badge bg-success">{{ approved_count }}✓</span>
                                        {% endif %}
                                        {% if pending_count > 0 %}
                                            <span class="badge bg-warning">{{ pending_count }}⏳</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('admin_view_user', user_id=student.user.id) }}"
                                           class="btn btn-outline-info" title="View Student Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('admin_edit_user', user_id=student.user.id) }}"
                                           class="btn btn-outline-primary" title="Edit Student">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% if assigned_students|length == 0 %}
                <div class="text-center py-4">
                    <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Students Assigned</h5>
                    <p class="text-muted">This supervisor has no students assigned yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- ITF State Students Summary -->
        {% if user.role.name == 'ITF Supervisor' and profile and stats.state_institution_summary %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>Students in {{ profile.assigned_state }} State
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Institutions in {{ profile.assigned_state }}</h6>
                        {% for institution, count in stats.state_institution_summary.items() %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ institution }}</span>
                            <span class="badge bg-primary">{{ count }} students</span>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Total:</strong> {{ stats.state_students }} students in {{ profile.assigned_state }} state
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Recent Student Logs for Students -->
        {% if user.role.name == 'Student' and stats.recent_logs %}
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Log Entries
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Activities</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in stats.recent_logs %}
                            <tr>
                                <td>{{ log.log_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <small>{{ log.activities_performed[:100] }}{% if log.activities_performed|length > 100 %}...{% endif %}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if log.status == 'Approved' else 'warning' if log.status == 'Pending' else 'danger' }}">
                                        {{ log.status }}
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted">{{ log.created_at.strftime('%Y-%m-%d') }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<style>
.avatar-circle-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2rem;
    object-fit: cover;
    border: 3px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    object-fit: cover;
    border: 2px solid #dee2e6;
}

.stat-item {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}
</style>
{% endblock %}
