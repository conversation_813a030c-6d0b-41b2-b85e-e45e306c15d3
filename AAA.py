import os

# Step 1: Construct the path to the text file on the desktop
# Replace 'YourUsername' with your actual Windows username
file_path = os.path.join(os.path.expanduser("~"), "Desktop", "SAMPLETEXT.txt")
# Step 2: Check if the file exists
if os.path.exists(file_path):
    print(f"Reading from file: {file_path}\n")
    
    # Step 3: Open and read the file
    with open(file_path, "r") as file:
        lines = file.readlines()
        
        print("File Contents:")
        for line_number, line in enumerate(lines, start=1):
            print(f"{line_number}: {line.strip()}")  # strip() removes newline characters
else:
    print(f"File not found at: {file_path}")
