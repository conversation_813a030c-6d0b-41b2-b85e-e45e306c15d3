{% extends "base.html" %}

{% block title %}ITF Dashboard - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('itf_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_students') }}">
                    <i class="fas fa-users me-2"></i>All Students
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_reports') }}">
                    <i class="fas fa-chart-bar me-2"></i>Reports
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_analytics') }}">
                    <i class="fas fa-chart-pie me-2"></i>Analytics
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('itf_profile') }}">
                    <i class="fas fa-user-edit me-2"></i>My Profile
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-chart-line me-2"></i> ITF Representative Dashboard
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('itf_reports') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-chart-bar me-1"></i>Generate Reports
            </a>
        </div>
    </div>
</div>

    <div class="row">
        <div class="col-md-3 mb-4">
            <div class="card text-white bg-primary stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Students</h5>
                            <h2 class="mb-0">{{ total_students }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-graduate fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card text-white bg-info stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Log Entries</h5>
                            <h2 class="mb-0">{{ total_logs }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card text-white bg-warning stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Pending Reviews</h5>
                            <h2 class="mb-0">{{ pending_logs }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card text-white bg-success stats-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Approved Logs</h5>
                            <h2 class="mb-0">{{ approved_logs }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>System Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Log Entry Status Distribution</h6>
                            <div class="progress mb-3">
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: {{ (approved_logs / total_logs * 100) if total_logs > 0 else 0 }}%">
                                    Approved ({{ approved_logs }})
                                </div>
                                <div class="progress-bar bg-warning" role="progressbar"
                                     style="width: {{ (pending_logs / total_logs * 100) if total_logs > 0 else 0 }}%">
                                    Pending ({{ pending_logs }})
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>System Activity</h6>
                            <p class="text-muted">Real-time monitoring and analytics coming soon...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>Recent Students
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_students %}
                        {% for student in recent_students %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ student.user.name }}</strong><br>
                                <small class="text-muted">{{ student.matric_number }}</small><br>
                                <small class="text-muted">{{ student.created_at.strftime('%Y-%m-%d') }}</small>
                            </div>
                        </div>
                        <hr>
                        {% endfor %}
                    {% else %}
                        <div class="text-center">
                            <i class="fas fa-users fa-2x text-muted mb-3"></i>
                            <p class="text-muted">No students registered yet</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
