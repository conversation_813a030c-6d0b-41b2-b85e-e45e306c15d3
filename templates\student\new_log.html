{% extends "base.html" %}

{% block title %}New Log Entry - SIWES Reporting System{% endblock %}

{% block sidebar %}
<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_dashboard') }}">
                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_profile') }}">
                    <i class="fas fa-user me-2"></i>Profile
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{{ url_for('new_log_entry') }}">
                    <i class="fas fa-plus me-2"></i>New Log Entry
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('view_school_supervisor') }}">
                    <i class="fas fa-chalkboard-teacher me-2"></i>School Supervisor
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{{ url_for('student_logs') }}">
                    <i class="fas fa-list me-2"></i>View Logs
                </a>
            </li>
        </ul>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>New Log Entry
    </h1>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Create Log Entry
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="log_date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="log_date" name="log_date"
                               value="{{ today().strftime('%Y-%m-%d') }}" required>
                        <div class="form-text">Select the date for this log entry.</div>
                    </div>

                    <div class="mb-3">
                        <label for="activities_performed" class="form-label">Activities Performed</label>
                        <textarea class="form-control" id="activities_performed" name="activities_performed"
                                  rows="6" required placeholder="Describe the activities you performed on this day..."></textarea>
                        <div class="form-text">Be specific and detailed about what you did during your SIWES.</div>
                    </div>

                    <div class="mb-3">
                        <label for="key_learnings" class="form-label">Key Learnings</label>
                        <textarea class="form-control" id="key_learnings" name="key_learnings"
                                  rows="4" placeholder="What did you learn from today's activities?"></textarea>
                        <div class="form-text">Reflect on what you learned and how it relates to your field of study.</div>
                    </div>

                    <!-- Media Upload Section -->
                    <div class="mb-4">
                        <label for="media_files" class="form-label">
                            <i class="fas fa-paperclip me-2"></i>Attach Media Files
                        </label>
                        <input type="file" class="form-control" id="media_files" name="media_files"
                               multiple accept=".png,.jpg,.jpeg,.gif,.bmp,.webp,.mp4,.avi,.mov,.wmv,.flv,.pdf,.doc,.docx">
                        <div class="form-text">
                            Upload images, videos, or documents to support your log entry.
                            <strong>Max file size: 50MB each.</strong>
                        </div>

                        <!-- File Preview Area -->
                        <div id="file-preview" class="mt-3" style="display: none;">
                            <h6>Selected Files:</h6>
                            <div id="file-list" class="row"></div>
                        </div>

                        <!-- Upload Guidelines -->
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center p-2">
                                            <i class="fas fa-image fa-2x text-primary mb-2"></i>
                                            <h6 class="small">Images</h6>
                                            <small class="text-muted">PNG, JPG, GIF, WebP</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center p-2">
                                            <i class="fas fa-video fa-2x text-success mb-2"></i>
                                            <h6 class="small">Videos</h6>
                                            <small class="text-muted">MP4, AVI, MOV, WMV</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light">
                                        <div class="card-body text-center p-2">
                                            <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                                            <h6 class="small">Documents</h6>
                                            <small class="text-muted">PDF, DOC, DOCX</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('student_logs') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>Back to Logs
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Log Entry
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Writing Tips
                </h5>
            </div>
            <div class="card-body">
                <h6>Activities Performed</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Be specific and detailed</li>
                    <li><i class="fas fa-check text-success me-2"></i>Include time spent on each task</li>
                    <li><i class="fas fa-check text-success me-2"></i>Mention tools/software used</li>
                    <li><i class="fas fa-check text-success me-2"></i>Note any challenges faced</li>
                </ul>

                <h6 class="mt-3">Key Learnings</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>Connect to academic knowledge</li>
                    <li><i class="fas fa-check text-success me-2"></i>Highlight new skills gained</li>
                    <li><i class="fas fa-check text-success me-2"></i>Reflect on problem-solving</li>
                    <li><i class="fas fa-check text-success me-2"></i>Consider future applications</li>
                </ul>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Student Info
                </h5>
            </div>
            <div class="card-body">
                <p><strong>Name:</strong> {{ current_user.name }}</p>
                <p><strong>Matric Number:</strong> {{ student.matric_number }}</p>
                <p><strong>Organization:</strong> {{ student.siwes_organization_name or 'Not set' }}</p>
                {% if student.siwes_start_date and student.siwes_end_date %}
                <p><strong>SIWES Period:</strong><br>
                {{ student.siwes_start_date.strftime('%Y-%m-%d') }} to
                {{ student.siwes_end_date.strftime('%Y-%m-%d') }}</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Important Notes
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <strong>Note:</strong> You can only create one log entry per date.
                        Make sure to review your entry before saving as you can only edit
                        entries that haven't been reviewed by supervisors.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Set today's date as default
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('log_date');
    if (!dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
});

// Character count for textareas
document.addEventListener('DOMContentLoaded', function() {
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(function(textarea) {
        const maxLength = 1000; // You can adjust this

        // Create character count element
        const countElement = document.createElement('div');
        countElement.className = 'form-text text-end';
        countElement.innerHTML = `<small>0 / ${maxLength} characters</small>`;
        textarea.parentNode.appendChild(countElement);

        // Update character count
        textarea.addEventListener('input', function() {
            const currentLength = this.value.length;
            countElement.innerHTML = `<small>${currentLength} / ${maxLength} characters</small>`;

            if (currentLength > maxLength * 0.9) {
                countElement.className = 'form-text text-end text-warning';
            } else {
                countElement.className = 'form-text text-end';
            }
        });
    });
});

// File upload preview functionality
document.getElementById('media_files').addEventListener('change', function(e) {
    const files = e.target.files;
    const filePreview = document.getElementById('file-preview');
    const fileList = document.getElementById('file-list');

    if (files.length > 0) {
        filePreview.style.display = 'block';
        fileList.innerHTML = '';

        Array.from(files).forEach((file, index) => {
            const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB
            const fileType = getFileType(file.name);
            const fileIcon = getFileIcon(fileType);

            const fileCard = document.createElement('div');
            fileCard.className = 'col-md-6 col-lg-4 mb-2';
            fileCard.innerHTML = `
                <div class="card">
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                            <i class="${fileIcon} fa-2x me-2"></i>
                            <div class="flex-grow-1">
                                <h6 class="mb-0 small">${file.name}</h6>
                                <small class="text-muted">${fileSize} MB</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            fileList.appendChild(fileCard);
        });
    } else {
        filePreview.style.display = 'none';
    }
});

function getFileType(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(ext)) {
        return 'image';
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(ext)) {
        return 'video';
    } else if (['pdf', 'doc', 'docx'].includes(ext)) {
        return 'document';
    }
    return 'other';
}

function getFileIcon(fileType) {
    switch(fileType) {
        case 'image': return 'fas fa-image text-primary';
        case 'video': return 'fas fa-video text-success';
        case 'document': return 'fas fa-file-alt text-info';
        default: return 'fas fa-file text-secondary';
    }
}
</script>
{% endblock %}
